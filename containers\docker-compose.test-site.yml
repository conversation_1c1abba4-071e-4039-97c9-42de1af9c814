networks:
  ai-coding-network:
    external: true
services:
  test-site:
    build:
      context: sites
      dockerfile: ../containers/Dockerfile.test-site
    container_name: site-test-site
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    env_file:
    - .env
    - .env.test-site
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:80/
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports:
    - 8080:80
    restart: unless-stopped
    volumes:
    - ./test-site:/app/test-site:ro
    - ./logs:/app/logs
version: '3.8'
