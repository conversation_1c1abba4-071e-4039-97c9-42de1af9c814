# Site Container Routes Integration

## Overview

The AI Coding Agent API now includes both basic and enhanced site container management routes to provide users with different levels of functionality based on their needs.

## Available Route Sets

### 1. Basic Site Container Routes (`/api/v1/site-containers`)

**File**: `api/site_container_routes.py`

**Purpose**: Core container management functionality

**Endpoints**:

- `POST /api/v1/site-containers/create` - Create a new site container
- `POST /api/v1/site-containers/{site_name}/start` - Start a container
- `POST /api/v1/site-containers/{site_name}/stop` - Stop a container
- `DELETE /api/v1/site-containers/{site_name}` - Delete a container
- `GET /api/v1/site-containers/` - List all containers
- `GET /api/v1/site-containers/{site_name}/status` - Get container status
- `POST /api/v1/site-containers/{site_name}/rebuild` - Rebuild a container
- `GET /api/v1/site-containers/{site_name}/logs` - Get container logs

**Use Case**: Simple container management with essential CRUD operations

### 2. Enhanced Site Container Routes (`/api/v1/enhanced-sites`)

**File**: `api/enhanced_site_container_routes.py`

**Purpose**: Advanced container management with additional features

**Endpoints**:

- All basic endpoints plus:
- `GET /api/v1/enhanced-sites/health` - Health check
- `GET /api/v1/enhanced-sites/status` - Overall status
- `POST /api/v1/enhanced-sites/containers/{site_name}/restart` - Restart container
- `GET /api/v1/enhanced-sites/monitor/sites` - Monitoring dashboard
- `GET /api/v1/enhanced-sites/ports` - Port allocations
- `POST /api/v1/enhanced-sites/containers/{site_name}/backup` - Create backup
- `POST /api/v1/enhanced-sites/containers/{site_name}/export` - Export site
- `POST /api/v1/enhanced-sites/containers/{site_name}/development` - Setup dev environment
- `POST /api/v1/enhanced-sites/containers/{site_name}/production` - Setup production environment
- `POST /api/v1/enhanced-sites/containers/{site_name}/ssl` - Configure SSL
- `GET /api/v1/enhanced-sites/dashboard` - Comprehensive dashboard

**Use Case**: Advanced container management with SSL, monitoring, backup, and deployment features

## Integration Details

### API Main Integration

Both route sets are included in `api/main.py`:

```python
# Include Site Container routes (basic)
try:
    from api.site_container_routes import router as site_container_router
    app.include_router(site_container_router, prefix="/api/v1")
    logger.info("Site Container routes (basic) included successfully")
except ImportError as e:
    logger.warning(f"Could not include Site Container routes (basic): {e}")

# Include Enhanced Site Container routes
try:
    from api.enhanced_site_container_routes import router as enhanced_site_container_router
    app.include_router(enhanced_site_container_router, prefix="/api/v1")
    logger.info("Enhanced Site Container routes included successfully")
except ImportError as e:
    logger.warning(f"Could not include Enhanced Site Container routes: {e}")
```

### Dependencies

Both route sets use the same underlying `SiteContainerManager` from `core.site_container_manager`, ensuring consistency in container operations.

## Usage Examples

### Basic Container Management

```bash
# Create a simple container
curl -X POST "http://localhost:8000/api/v1/site-containers/create" \
  -H "Content-Type: application/json" \
  -d '{"site_name": "my-site", "environment": "production"}'

# List all containers
curl "http://localhost:8000/api/v1/site-containers/"

# Get container status
curl "http://localhost:8000/api/v1/site-containers/my-site/status"
```

### Enhanced Container Management

```bash
# Create container with advanced configuration
curl -X POST "http://localhost:8000/api/v1/enhanced-sites/containers" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-advanced-site",
    "environment": "production",
    "ssl_enabled": true,
    "backup_enabled": true,
    "monitoring_enabled": true
  }'

# Setup development environment
curl -X POST "http://localhost:8000/api/v1/enhanced-sites/containers/my-site/development"

# Configure SSL
curl -X POST "http://localhost:8000/api/v1/enhanced-sites/containers/my-site/ssl" \
  -H "Content-Type: application/json" \
  -d '{"provider": "lets_encrypt"}'

# Get comprehensive dashboard
curl "http://localhost:8000/api/v1/enhanced-sites/dashboard"
```

## Feature Comparison

| Feature | Basic Routes | Enhanced Routes |
|---------|-------------|-----------------|
| CRUD Operations | ✅ | ✅ |
| Container Status | ✅ | ✅ |
| Logs | ✅ | ✅ |
| SSL Configuration | ❌ | ✅ |
| Backup Management | ❌ | ✅ |
| Export Functionality | ❌ | ✅ |
| Development Setup | ❌ | ✅ |
| Production Setup | ❌ | ✅ |
| Monitoring Dashboard | ❌ | ✅ |
| Port Management | ❌ | ✅ |
| Health Checks | ❌ | ✅ |

### 3. Phase 19: Additional API Routes

- AI Container Management (`/api/ai-containers`)
  - POST /api/ai-containers/analyze/{site_name}
  - POST /api/ai-containers/optimize/{site_name}
  - GET  /api/ai-containers/confidence/{site_name}
  - POST /api/ai-containers/generate-dockerfile/{site_name}

- Monitoring Dashboard (`/api/monitoring`)
  - GET /api/monitoring/dashboard/data
  - GET /api/monitoring/containers/{container_name}/metrics
  - GET /api/monitoring/health-checks
  - GET /api/monitoring/system-status
  - GET /api/monitoring/alerts

- Port Management (`/api/ports`)
  - POST /api/ports/allocate/{site_name}
  - POST /api/ports/release/{site_name}
  - GET  /api/ports/list
  - GET  /api/ports/available

- Health Check (`/api/health`)
  - GET  /api/health/containers
  - GET  /api/health/containers/{container_name}
  - POST /api/health/configure/{container_name}
  - GET  /api/health/summary

- Alert Management (`/api/alerts`)
  - GET  /api/alerts/
  - GET  /api/alerts/{alert_id}
  - POST /api/alerts/{alert_id}/acknowledge

| Comprehensive Dashboard | ❌ | ✅ |

## Recommendations

1. **For Simple Use Cases**: Use the basic routes (`/api/v1/site-containers`) for straightforward container management
2. **For Advanced Use Cases**: Use the enhanced routes (`/api/v1/enhanced-sites`) for production deployments with SSL, monitoring, and backup features
3. **For Development**: Start with basic routes and migrate to enhanced routes as your needs grow
4. **For Production**: Use enhanced routes for comprehensive container lifecycle management

## Testing

Both route sets are tested independently:

- Basic routes: `tests/test_site_container_api.py`
- Enhanced routes: Can be tested via the API documentation at `/docs`

## Future Enhancements

- Consider consolidating both route sets into a single comprehensive API
- Add more advanced features like auto-scaling and load balancing
- Implement container orchestration for multi-container applications
- Add support for Kubernetes deployment

## Migration Path

If you're currently using basic routes and want to upgrade to enhanced features:

1. **Gradual Migration**: Keep using basic routes for existing containers
2. **New Containers**: Use enhanced routes for new deployments
3. **Feature Adoption**: Gradually adopt enhanced features as needed
4. **Full Migration**: Eventually migrate all containers to use enhanced routes

This approach ensures backward compatibility while providing access to advanced features when needed.
