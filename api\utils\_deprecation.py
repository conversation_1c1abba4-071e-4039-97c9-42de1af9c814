"""
Deprecation utilities for the API module.

This module provides utilities for handling deprecated methods and functions.
"""

import functools
import warnings
from typing import Any, Callable, Optional


def _deprecate_method(
    version: str,
    message: Optional[str] = None,
    category: type = DeprecationWarning
) -> Callable:
    """
    Decorator to mark methods as deprecated.
    
    Args:
        version: The version in which the method was deprecated
        message: Optional custom deprecation message
        category: Warning category to use (default: DeprecationWarning)
    
    Returns:
        Decorated function that issues deprecation warning when called
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            warning_message = message or f"{func.__name__} is deprecated since version {version}"
            warnings.warn(
                warning_message,
                category=category,
                stacklevel=2
            )
            return func(*args, **kwargs)
        return wrapper
    return decorator


def deprecate_function(
    func: Callable,
    version: str,
    message: Optional[str] = None,
    category: type = DeprecationWarning
) -> Callable:
    """
    Mark a function as deprecated.
    
    Args:
        func: Function to deprecate
        version: The version in which the function was deprecated
        message: Optional custom deprecation message
        category: Warning category to use (default: DeprecationWarning)
    
    Returns:
        Decorated function that issues deprecation warning when called
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        warning_message = message or f"{func.__name__} is deprecated since version {version}"
        warnings.warn(
            warning_message,
            category=category,
            stacklevel=2
        )
        return func(*args, **kwargs)
    return wrapper
