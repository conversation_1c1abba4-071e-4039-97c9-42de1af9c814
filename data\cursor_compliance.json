{"last_check": "2025-08-08T00:26:33.707875", "violations": ["Test failures detected - fix before proceeding"], "compliance_score": 90.9090909090909, "rule_checks": {"file_organization": {"status": "passed", "violations": [], "warnings": ["Script file validation_service.py should be in scripts/ directory"]}, "todo_completion": {"status": "passed", "violations": [], "warnings": [], "incomplete_todos": []}, "test_success": {"status": "failed", "violations": ["Tests are failing - 100% success rate required"], "warnings": [], "test_results": {}}, "dependency_management": {"status": "passed", "violations": [], "warnings": ["Dependency pydantic>=2.11.7,<3.0.0 should use exact version pinning (==)", "Dependency pydantic-settings>=2.8.0 should use exact version pinning (==)", "Dependency pydantic_core>=2.33.2 should use exact version pinning (==)", "Dependency realtime>=2.6.0 should use exact version pinning (==)"]}, "cli_api_compliance": {"status": "passed", "violations": [], "warnings": []}, "ai_model_compliance": {"status": "passed", "violations": [], "warnings": ["Cloud model usage detected in api\\main.py (future mode will allow this)", "Cloud model usage detected in api\\main.py (future mode will allow this)", "Cloud model usage detected in api\\main.py (future mode will allow this)", "Cloud model usage detected in content\\cms_content_manager.py (future mode will allow this)", "Cloud model usage detected in content\\cms_content_manager.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\evaluator.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\pipeline.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\trainer.py (future mode will allow this)", "Cloud model usage detected in scripts\\train_model.py (future mode will allow this)", "Cloud model usage detected in tests\\test_fine_tuning.py (future mode will allow this)", "Cloud model usage detected in tests\\test_ollama_integration.py (future mode will allow this)", "Cloud model usage detected in tests\\test_ollama_integration.py (future mode will allow this)", "Cloud model usage detected in tests\\test_pippy.py (future mode will allow this)"], "current_mode": "local_only"}, "git_workflow": {"status": "passed", "violations": [], "warnings": ["Repository should be in G:\\AICodingAgent, found in F:/NasShare/AICodingAgent", "There are uncommitted changes"]}, "security_compliance": {"status": "passed", "violations": [], "warnings": ["Potential hardcoded secret in api\\hf_api.py"]}, "file_cleanup": {"status": "passed", "violations": [], "warnings": ["Found 17 potential duplicate files", "Found 1 potentially obsolete files"], "duplicates": 17, "obsolete_files": 1}, "mock_data_cleanup": {"status": "passed", "violations": [], "warnings": ["Found 282 potential mock data files"], "mock_files": 282}, "virtual_environment": {"status": "passed", "violations": [], "warnings": []}}, "warnings": []}