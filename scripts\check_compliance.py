#!/usr/bin/env python3
"""
Simple script to check cursor rules compliance
"""

import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:

    def main():
        """
        Main function to check cursor rules compliance.

        Creates a CursorRulesEnforcer instance, runs compliance checks,
        and displays the results including compliance score, violations,
        and warnings.

        Returns:
            dict: Compliance check results containing score, status, violations, and warnings
        """
        print("🔍 Testing Cursor Rules Compliance...")

        from core.cursor_rules_enforcer import CursorRulesEnforcer

        # Create enforcer
        enforcer = CursorRulesEnforcer()

        # Run compliance check
        result = enforcer.check_compliance()

        # Display results
        print(f"✅ Compliance Score: {result.get('compliance_score', 0):.1f}%")
        print(f"Status: {result.get('status', 'unknown')}")
        print(f"Critical Violations: {len(result.get('violations', []))}")
        print(f"Warnings: {len(result.get('warnings', []))}")

        # Show violations
        violations = result.get("violations", [])
        if violations:
            print("\n🚨 Critical Violations:")
            for i, violation in enumerate(violations, 1):
                print(f"  {i}. {violation}")
        else:
            print("\n✅ No critical violations found!")

        # Show warnings
        warnings = result.get("warnings", [])
        if warnings:
            print("\n⚠️  Warnings:")
            for i, warning in enumerate(warnings, 1):
                print(f"  {i}. {warning}")
        else:
            print("\n✅ No warnings found!")

        return result

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ Failed to import CursorRulesEnforcer: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error running compliance check: {e}")
    sys.exit(1)
