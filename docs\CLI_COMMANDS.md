# 🖥️ CLI Commands Reference

This document provides comprehensive documentation for all CLI commands available in the AI Coding Agent.

## 🚀 **Container Management Commands**

### **Site Container Operations**

#### `create-site-container`
Create a new Docker container for a website.

```bash
python cli.py create-site-container <site_name> --port <port> --environment <env>
```

**Parameters:**
- `site_name` (required): Name of the site to containerize
- `--port` (optional): External port for the container (auto-allocated if not specified)
- `--environment` (optional): Environment type (development, staging, production)

**Example:**
```bash
python cli.py create-site-container my-react-app --port 8080 --environment production
```

#### `create-optimized-container`
Create an AI-optimized container with intelligent analysis.

```bash
python cli.py create-optimized-container <site_name> --optimize --model <model_name>
```

**Parameters:**
- `site_name` (required): Name of the site to containerize
- `--optimize` (flag): Enable AI optimization
- `--model` (optional): Ollama model to use (default: deepseek-coder:1.3b)

**Example:**
```bash
python cli.py create-optimized-container my-vue-app --optimize --model deepseek-coder:1.3b
```

#### `analyze-site-performance`
Analyze site performance and get optimization recommendations.

```bash
python cli.py analyze-site-performance <site_name> --detailed --model <model_name>
```

**Parameters:**
- `site_name` (required): Name of the site to analyze
- `--detailed` (flag): Include detailed analysis
- `--model` (optional): Ollama model to use

**Example:**
```bash
python cli.py analyze-site-performance my-site --detailed --model yi-coder:1.5b
```

## 📊 **Monitoring Commands**

### **System Monitoring**

#### `start-monitoring`
Start the comprehensive container monitoring system.

```bash
python cli.py start-monitoring --port <dashboard_port>
```

**Parameters:**
- `--port` (optional): Port for the monitoring dashboard (default: 8090)

**Example:**
```bash
python cli.py start-monitoring --port 8090
```

**Features Started:**
- Container metrics collection
- Health check monitoring
- Alert system
- Web dashboard at `http://localhost:8090`

#### `stop-monitoring`
Stop the container monitoring system.

```bash
python cli.py stop-monitoring
```

#### `get-monitoring-status`
Get the current status of the monitoring system.

```bash
python cli.py get-monitoring-status
```

**Returns:**
- Monitoring system status
- Active components
- Current metrics summary
- Alert summary

### **Container Performance**

#### `get-container-performance`
Get detailed performance metrics for a specific container.

```bash
python cli.py get-container-performance <container_name> --minutes <time_period>
```

**Parameters:**
- `container_name` (required): Name of the container
- `--minutes` (optional): Time period for metrics (default: 60)

**Example:**
```bash
python cli.py get-container-performance site-myapp --minutes 120
```

**Returns:**
- CPU and memory usage statistics
- Network I/O metrics
- Health check results
- Active alerts
- Performance trends

## 🔧 **Port Management Commands**

### **Port Operations**

#### `allocate-port`
Allocate a port for a site.

```bash
python cli.py allocate-port <site_name>
```

#### `release-port`
Release a port allocation.

```bash
python cli.py release-port <site_name>
```

#### `list-ports`
List all port allocations.

```bash
python cli.py list-ports
```

#### `get-available-ports`
Get list of available ports.

```bash
python cli.py get-available-ports
```

## 🤖 **AI Integration Commands**

### **AI Analysis**

#### `ai-analyze-site`
Perform AI-powered site analysis.

```bash
python cli.py ai-analyze-site <site_name> --model <model_name>
```

**Parameters:**
- `site_name` (required): Name of the site to analyze
- `--model` (optional): Ollama model to use

**Features:**
- Framework detection
- Complexity scoring
- Security analysis
- Performance recommendations
- Optimization suggestions

#### `generate-dockerfile`
Generate an optimized Dockerfile using AI.

```bash
python cli.py generate-dockerfile <site_name> --strategy <strategy>
```

**Parameters:**
- `site_name` (required): Name of the site
- `--strategy` (optional): Build strategy (auto, multi-stage, single-stage)

## 🔍 **Diagnostic Commands**

### **System Diagnostics**

#### `check-docker-status`
Check Docker system status and connectivity.

```bash
python cli.py check-docker-status
```

#### `validate-environment`
Validate the development environment setup.

```bash
python cli.py validate-environment
```

#### `test-ai-models`
Test availability of AI models.

```bash
python cli.py test-ai-models
```

## 📋 **Information Commands**

### **System Information**

#### `list-sites`
List all available sites.

```bash
python cli.py list-sites
```

#### `list-containers`
List all active containers.

```bash
python cli.py list-containers
```

#### `get-system-info`
Get comprehensive system information.

```bash
python cli.py get-system-info
```

## 🚨 **Alert Management Commands**

### **Alert Operations**

#### `list-alerts`
List all active alerts.

```bash
python cli.py list-alerts --severity <level>
```

**Parameters:**
- `--severity` (optional): Filter by severity (low, medium, high, critical)

#### `acknowledge-alert`
Acknowledge an alert.

```bash
python cli.py acknowledge-alert <alert_id>
```

#### `configure-alerts`
Configure alert rules.

```bash
python cli.py configure-alerts --config <config_file>
```

## 🔧 **Configuration Commands**

### **System Configuration**

#### `set-config`
Set configuration values.

```bash
python cli.py set-config <key> <value>
```

#### `get-config`
Get configuration values.

```bash
python cli.py get-config <key>
```

#### `reset-config`
Reset configuration to defaults.

```bash
python cli.py reset-config
```

## 📚 **Help and Documentation**

### **Getting Help**

#### `help`
Show general help information.

```bash
python cli.py help
```

#### `help <command>`
Show help for a specific command.

```bash
python cli.py help create-site-container
```

## 🔄 **Workflow Examples**

### **Complete Site Deployment**

```bash
# 1. Create optimized container
python cli.py create-optimized-container my-app --optimize

# 2. Start monitoring
python cli.py start-monitoring

# 3. Check performance
python cli.py get-container-performance site-my-app

# 4. Monitor via dashboard
# Open http://localhost:8090 in browser
```

### **Performance Analysis Workflow**

```bash
# 1. Analyze site performance
python cli.py analyze-site-performance my-app --detailed

# 2. Get monitoring status
python cli.py get-monitoring-status

# 3. Check for alerts
python cli.py list-alerts

# 4. View container metrics
python cli.py get-container-performance site-my-app --minutes 240
```

### **Troubleshooting Workflow**

```bash
# 1. Check system status
python cli.py check-docker-status

# 2. Validate environment
python cli.py validate-environment

# 3. List containers
python cli.py list-containers

# 4. Check alerts
python cli.py list-alerts --severity high
```

## 🎯 **Best Practices**

### **Command Usage Tips**

1. **Always activate virtual environment** before running commands
2. **Use monitoring** for production deployments
3. **Enable AI optimization** for better performance
4. **Check alerts regularly** for proactive maintenance
5. **Use detailed analysis** for optimization insights

### **Performance Optimization**

1. **Start with AI analysis** to understand your site
2. **Use optimized containers** for better resource usage
3. **Monitor continuously** to catch issues early
4. **Review alerts** to improve configurations
5. **Analyze trends** for capacity planning

---

For more detailed information about specific features, see:
- [Docker Setup Guide](../README_Docker.md)
- [Monitoring Guide](MONITORING_GUIDE.md)
- [Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md)
