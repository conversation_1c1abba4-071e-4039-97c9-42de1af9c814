"""
External Hosting CLI Commands
Provides command-line interface for external hosting exports and deployments.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, Optional

from core.external_hosting_manager import (
    ExportConfig,
    ExternalHostingManager,
    HostingProvider,
)
from core.ai_container_manager import AIEnhancedContainerManager
from monitoring.alerting_system import MonitoringManager

logger = logging.getLogger(__name__)


class ExternalHostingCommands:
    """CLI commands for external hosting management"""

    def __init__(self, agent):
        self.agent = agent
        self.hosting_manager = ExternalHostingManager()
        self.ai_container_manager = AIEnhancedContainerManager()
        self.monitoring_manager = MonitoringManager()

    async def export_site(
        self, site_name: str, provider: str, **kwargs
    ) -> Dict[str, Any]:
        """Export a site for external hosting"""
        try:
            # Validate provider
            try:
                hosting_provider = HostingProvider(provider.lower())
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid provider: {provider}. Supported providers: {[p.value for p in HostingProvider]}",
                }

            # Create export configuration
            config = ExportConfig(
                site_name=site_name,
                provider=hosting_provider,
                optimize=kwargs.get("optimize", True),
                minify=kwargs.get("minify", True),
                compress=kwargs.get("compress", True),
                custom_domain=kwargs.get("custom_domain"),
                environment=kwargs.get("environment", "production"),
            )

            # Export the site
            result = await self.hosting_manager.export_site(
                site_name, hosting_provider, config
            )

            if result.success:
                logger.info(f"✅ Site {site_name} exported to {provider}")
                return {
                    "success": True,
                    "message": f"Site {site_name} exported to {provider}",
                    "export_path": result.export_path,
                    "deployment_url": result.deployment_url,
                    "provider": provider,
                    "build_artifacts": result.build_artifacts,
                    "export_time": (
                        result.export_time.isoformat() if result.export_time else None
                    ),
                }
            else:
                logger.error(f"❌ Failed to export site: {result.error_message}")
                return {"success": False, "error": result.error_message}

        except Exception as e:
            logger.error(f"Error exporting site: {e}")
            return {"success": False, "error": str(e)}

    async def export_to_netlify(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site to Netlify"""
        return await self.export_site(site_name, "netlify", **kwargs)

    async def export_to_github_pages(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site to GitHub Pages"""
        return await self.export_site(site_name, "github_pages", **kwargs)

    async def export_to_vercel(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site to Vercel"""
        return await self.export_site(site_name, "vercel", **kwargs)

    async def export_static(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site as static files"""
        return await self.export_site(site_name, "static_export", **kwargs)

    async def list_exports(
        self, site_name: Optional[str] = None, **_kwargs
    ) -> Dict[str, Any]:
        """List available exports"""
        try:
            exports = await self.hosting_manager.list_exports(site_name)

            if exports:
                logger.info(f"✅ Found {len(exports)} exports")
                return {"success": True, "exports": exports, "count": len(exports)}
            else:
                logger.info("ℹ️ No exports found")
                return {
                    "success": True,
                    "exports": [],
                    "count": 0,
                    "message": "No exports found",
                }

        except Exception as e:
            logger.error(f"Error listing exports: {e}")
            return {"success": False, "error": str(e)}

    async def delete_export(self, export_name: str, **_kwargs) -> Dict[str, Any]:
        """Delete an export"""
        try:
            success = await self.hosting_manager.delete_export(export_name)

            if success:
                logger.info(f"✅ Deleted export: {export_name}")
                return {
                    "success": True,
                    "message": f"Export {export_name} deleted successfully",
                }
            else:
                logger.error(f"❌ Failed to delete export: {export_name}")
                return {
                    "success": False,
                    "error": f"Failed to delete export: {export_name}",
                }

        except Exception as e:
            logger.error(f"Error deleting export: {e}")
            return {"success": False, "error": str(e)}

    async def configure_provider(self, provider: str, **kwargs) -> Dict[str, Any]:
        """Configure a hosting provider"""
        try:
            # Validate provider
            try:
                HostingProvider(provider.lower())
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid provider: {provider}. Supported providers: {[p.value for p in HostingProvider]}",
                }

            # Load current configuration
            import json
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                with open(config_file, "r") as f:
                    config = json.load(f)
            else:
                config = {}

            # Update provider configuration
            provider_key = provider.lower()
            if provider_key not in config:
                config[provider_key] = {}

            # Update with provided parameters
            for key, value in kwargs.items():
                if value is not None:  # Only update non-None values
                    config[provider_key][key] = value

            # Enable the provider
            config[provider_key]["enabled"] = True

            # Save configuration
            with open(config_file, "w") as f:
                json.dump(config, f, indent=2)

            logger.info(f"✅ Configured {provider} hosting provider")
            return {
                "success": True,
                "message": f"Provider {provider} configured successfully",
                "provider": provider,
                "enabled": True,
            }

        except Exception as e:
            logger.error(f"Error configuring provider: {e}")
            return {"success": False, "error": str(e)}

    async def get_provider_status(self, provider: str, **_kwargs) -> Dict[str, Any]:
        """Get status of a hosting provider"""
        try:
            # Validate provider
            try:
                HostingProvider(provider.lower())
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid provider: {provider}. Supported providers: {[p.value for p in HostingProvider]}",
                }

            # Load configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)

                provider_config = config.get(provider.lower(), {})

                return {
                    "success": True,
                    "provider": provider,
                    "enabled": provider_config.get("enabled", False),
                    "configured": bool(provider_config),
                    "configuration": provider_config,
                }
            else:
                return {
                    "success": True,
                    "provider": provider,
                    "enabled": False,
                    "configured": False,
                    "configuration": {},
                }

        except Exception as e:
            logger.error(f"Error getting provider status: {e}")
            return {"success": False, "error": str(e)}

    async def list_providers(self, **_kwargs) -> Dict[str, Any]:
        """List all available hosting providers"""
        try:
            providers = []
            for provider in HostingProvider:
                status = await self.get_provider_status(provider.value)
                if status["success"]:
                    providers.append(
                        {
                            "name": provider.value,
                            "enabled": status["enabled"],
                            "configured": status["configured"],
                        }
                    )

            return {"success": True, "providers": providers, "count": len(providers)}

        except Exception as e:
            logger.error(f"Error listing providers: {e}")
            return {"success": False, "error": str(e)}

    async def test_provider_connection(self, provider: str, **_kwargs) -> Dict[str, Any]:
        """Test connection to a hosting provider"""
        try:
            # Validate provider
            try:
                HostingProvider(provider.lower())
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid provider: {provider}. Supported providers: {[p.value for p in HostingProvider]}",
                }

            # Get provider status
            status = await self.get_provider_status(provider)
            if not status["success"]:
                return status

            if not status["enabled"]:
                return {
                    "success": False,
                    "error": f"Provider {provider} is not enabled",
                }

            # Test connection based on provider
            if provider.lower() == "netlify":
                return await self._test_netlify_connection()
            elif provider.lower() == "github_pages":
                return await self._test_github_connection()
            elif provider.lower() == "vercel":
                return await self._test_vercel_connection()
            else:
                return {
                    "success": True,
                    "message": f"Provider {provider} connection test not implemented",
                    "provider": provider,
                }

        except Exception as e:
            logger.error(f"Error testing provider connection: {e}")
            return {"success": False, "error": str(e)}

    async def _test_netlify_connection(self) -> Dict[str, Any]:
        """Test Netlify connection"""
        try:
            # Load configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)

                netlify_config = config.get("netlify", {})
                api_token = netlify_config.get("api_token")

                if not api_token:
                    return {
                        "success": False,
                        "error": "Netlify API token not configured",
                    }

                # Test API connection
                import requests

                headers = {"Authorization": f"Bearer {api_token}"}
                response = requests.get(
                    "https://api.netlify.com/api/v1/user", headers=headers
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "Netlify connection successful",
                        "provider": "netlify",
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Netlify API error: {response.status_code}",
                    }
            else:
                return {"success": False, "error": "Configuration file not found"}

        except Exception as e:
            return {
                "success": False,
                "error": f"Netlify connection test failed: {str(e)}",
            }

    async def _test_github_connection(self) -> Dict[str, Any]:
        """Test GitHub connection"""
        try:
            # Load configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)

                github_config = config.get("github_pages", {})
                token = github_config.get("token")
                repository = github_config.get("repository")

                if not token or not repository:
                    return {
                        "success": False,
                        "error": "GitHub token or repository not configured",
                    }

                # Test API connection
                import requests

                headers = {"Authorization": f"token {token}"}
                response = requests.get(
                    f"https://api.github.com/repos/{repository}", headers=headers
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "GitHub connection successful",
                        "provider": "github_pages",
                    }
                else:
                    return {
                        "success": False,
                        "error": f"GitHub API error: {response.status_code}",
                    }
            else:
                return {"success": False, "error": "Configuration file not found"}

        except Exception as e:
            return {
                "success": False,
                "error": f"GitHub connection test failed: {str(e)}",
            }

    async def _test_vercel_connection(self) -> Dict[str, Any]:
        """Test Vercel connection"""
        try:
            # Load configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)

                vercel_config = config.get("vercel", {})
                api_token = vercel_config.get("api_token")

                if not api_token:
                    return {
                        "success": False,
                        "error": "Vercel API token not configured",
                    }

                # Test API connection
                import requests

                headers = {"Authorization": f"Bearer {api_token}"}
                response = requests.get(
                    "https://api.vercel.com/v1/user", headers=headers
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "Vercel connection successful",
                        "provider": "vercel",
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Vercel API error: {response.status_code}",
                    }
            else:
                return {"success": False, "error": "Configuration file not found"}

        except Exception as e:
            return {
                "success": False,
                "error": f"Vercel connection test failed: {str(e)}",
            }

    async def create_optimized_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Create optimized container with AI enhancements"""
        try:
            optimize = kwargs.get("optimize", True)
            ollama_model = kwargs.get("model", "deepseek-coder:1.3b")

            logger.info(f"🤖 Creating optimized container for {site_name} (AI: {optimize})")

            # Initialize AI container manager with specified model
            self.ai_container_manager.ollama_model = ollama_model

            site_config = {
                "name": site_name,
                "environment": kwargs.get("environment", "production"),
                "optimization_enabled": optimize
            }

            if optimize:
                # Get AI analysis first
                analysis = await self.ai_container_manager.analyze_and_optimize_site(site_name)

                if analysis["success"]:
                    logger.info(f"✅ AI Analysis completed for {site_name}")
                    logger.info(f"📊 Framework: {analysis.get('analysis', {}).get('framework', 'unknown')}")
                    logger.info(f"🔧 Strategy: {analysis.get('dockerfile_strategy', 'standard')}")
                    logger.info(f"🛡️ Security recommendations: {len(analysis.get('security_hardening', []))}")
                    logger.info(f"⚡ Performance tips: {len(analysis.get('performance_optimizations', []))}")

                    # Create optimized container
                    result = await self.ai_container_manager.create_optimized_site_container(
                        site_name, site_config, use_ai_optimization=True
                    )

                    if result["success"]:
                        return {
                            "success": True,
                            "message": f"Optimized container created for {site_name}",
                            "ai_analysis": analysis,
                            "container": result.get("container"),
                            "port": result.get("port"),
                            "optimizations_applied": True
                        }
                    else:
                        return result
                else:
                    logger.warning(f"⚠️ AI analysis failed for {site_name}, using standard approach")
                    # Fall back to standard container creation
                    result = await self.ai_container_manager.create_site_container(site_name, site_config)
                    return {
                        "success": result["success"],
                        "message": f"Standard container created for {site_name} (AI analysis failed)",
                        "container": result.get("container"),
                        "port": result.get("port"),
                        "optimizations_applied": False,
                        "ai_error": analysis.get("error", "Unknown AI error")
                    }
            else:
                # Create standard container
                result = await self.ai_container_manager.create_site_container(site_name, site_config)
                return {
                    "success": result["success"],
                    "message": f"Standard container created for {site_name}",
                    "container": result.get("container"),
                    "port": result.get("port"),
                    "optimizations_applied": False
                }

        except Exception as e:
            logger.error(f"Error creating optimized container: {e}")
            return {"success": False, "error": str(e)}

    async def analyze_site_performance(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Analyze site performance and provide optimization recommendations"""
        try:
            ollama_model = kwargs.get("model", "deepseek-coder:1.3b")
            detailed = kwargs.get("detailed", False)

            logger.info(f"🔍 Analyzing site performance for {site_name}")

            # Initialize AI container manager with specified model
            self.ai_container_manager.ollama_model = ollama_model

            # Perform AI analysis
            analysis = await self.ai_container_manager.analyze_and_optimize_site(site_name)

            if analysis["success"]:
                # Format analysis results
                performance_report = {
                    "site_name": site_name,
                    "analysis_timestamp": analysis.get("analysis", {}).get("timestamp"),
                    "framework": analysis.get("analysis", {}).get("framework", "unknown"),
                    "complexity_score": analysis.get("analysis", {}).get("complexity_score", 0),
                    "build_complexity": analysis.get("analysis", {}).get("build_complexity", "unknown"),
                    "optimization_potential": analysis.get("analysis", {}).get("optimization_potential", "unknown"),
                    "recommended_strategy": analysis.get("dockerfile_strategy", "standard"),
                    "estimated_build_time": analysis.get("estimated_build_time", "unknown"),
                    "ai_confidence": analysis.get("ai_confidence", 0.0),
                    "security_recommendations": analysis.get("security_hardening", []),
                    "performance_optimizations": analysis.get("performance_optimizations", []),
                    "recommended_resources": analysis.get("recommended_resources", {})
                }

                if detailed:
                    performance_report["detailed_analysis"] = analysis.get("analysis", {})

                logger.info(f"✅ Performance analysis completed for {site_name}")
                logger.info(f"📊 Complexity Score: {performance_report['complexity_score']}/10")
                logger.info(f"🔧 Recommended Strategy: {performance_report['recommended_strategy']}")
                logger.info(f"⚡ Optimization Potential: {performance_report['optimization_potential']}")

                return {
                    "success": True,
                    "performance_report": performance_report
                }
            else:
                return {
                    "success": False,
                    "error": analysis.get("error", "Performance analysis failed")
                }

        except Exception as e:
            logger.error(f"Error analyzing site performance: {e}")
            return {"success": False, "error": str(e)}

    async def start_monitoring(self, **kwargs) -> Dict[str, Any]:
        """Start comprehensive container monitoring system"""
        try:
            dashboard_port = kwargs.get("port", 8090)

            logger.info(f"🚀 Starting container monitoring system on port {dashboard_port}")

            # Update dashboard port if specified
            self.monitoring_manager.dashboard.port = dashboard_port

            # Start all monitoring components
            await self.monitoring_manager.start_all_monitoring()

            return {
                "success": True,
                "message": "Container monitoring system started successfully",
                "dashboard_url": f"http://localhost:{dashboard_port}",
                "components": {
                    "metrics_collector": "Started - collecting container performance metrics",
                    "health_system": "Started - monitoring container health checks",
                    "alerting_system": "Started - monitoring for alerts and notifications",
                    "dashboard": f"Started - web dashboard available at http://localhost:{dashboard_port}"
                }
            }

        except Exception as e:
            logger.error(f"Error starting monitoring system: {e}")
            return {"success": False, "error": str(e)}

    async def stop_monitoring(self) -> Dict[str, Any]:
        """Stop container monitoring system"""
        try:
            logger.info("🛑 Stopping container monitoring system")

            await self.monitoring_manager.stop_all_monitoring()

            return {
                "success": True,
                "message": "Container monitoring system stopped successfully"
            }

        except Exception as e:
            logger.error(f"Error stopping monitoring system: {e}")
            return {"success": False, "error": str(e)}

    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get status of monitoring system"""
        try:
            status = self.monitoring_manager.get_monitoring_status()

            # Get additional metrics
            if status["monitoring_active"]:
                metrics = self.monitoring_manager.metrics_collector.get_all_current_metrics()
                health = self.monitoring_manager.health_system.get_all_health_status()
                alerts = self.monitoring_manager.alerting_system.get_alert_summary()

                status.update({
                    "current_metrics": {
                        "total_containers": len(metrics),
                        "containers": list(metrics.keys())
                    },
                    "health_status": {
                        "total_containers": len(health),
                        "healthy": len([h for h in health.values() if h.get("status") == "healthy"]),
                        "unhealthy": len([h for h in health.values() if h.get("status") == "unhealthy"])
                    },
                    "alerts": alerts
                })

            return {
                "success": True,
                "monitoring_status": status
            }

        except Exception as e:
            logger.error(f"Error getting monitoring status: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_performance(self, container_name: str, **kwargs) -> Dict[str, Any]:
        """Get detailed performance metrics for a specific container"""
        try:
            minutes = kwargs.get("minutes", 60)

            if not self.monitoring_manager.is_running:
                return {
                    "success": False,
                    "error": "Monitoring system is not running. Start it first with start_monitoring command."
                }

            # Get performance summary
            performance = self.monitoring_manager.metrics_collector.get_performance_summary(
                container_name, minutes
            )

            # Get health summary
            health = self.monitoring_manager.health_system.get_health_summary(
                container_name, minutes
            )

            # Get active alerts for this container
            active_alerts = [
                alert for alert in self.monitoring_manager.alerting_system.get_active_alerts()
                if alert.container_name == container_name
            ]

            return {
                "success": True,
                "container_name": container_name,
                "performance_metrics": performance,
                "health_metrics": health,
                "active_alerts": [
                    {
                        "id": alert.id,
                        "type": alert.alert_type.value,
                        "severity": alert.severity.value,
                        "message": alert.message,
                        "timestamp": alert.timestamp.isoformat()
                    }
                    for alert in active_alerts
                ],
                "period_minutes": minutes
            }

        except Exception as e:
            logger.error(f"Error getting container performance: {e}")
            return {"success": False, "error": str(e)}
