[tool:pytest]
# Disable faulthandler to prevent Windows access violations
addopts =
    -p no:faulthandler
    -p no:warnings
    --tb=short
    -v
    --strict-markers
    --disable-warnings

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    skip_windows: mark test to skip on Windows due to access violations
    skip_win32: mark test to skip Windows-specific functionality
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning

# Minimum version
minversion = 6.0
