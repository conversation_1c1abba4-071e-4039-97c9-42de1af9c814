"""
Docker Container Management System
Handles general container creation, deployment, and orchestration.

NOTE: This is the general-purpose Docker manager.
For website-specific container management, use core/site_container_manager.py
which provides specialized features like SSL, monitoring, and hot reload.
"""

import json
import logging
import os
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml

logger = logging.getLogger(__name__)


class DockerManager:
    """Main Docker container management system"""

    def __init__(self):
        self.config_path = Path("config/containerization_config.json")
        self.dockerfiles_path = Path("docker")
        self.compose_path = Path("docker-compose.yml")
        self._ensure_directories()

    def _ensure_directories(self):
        """Ensure required directories exist"""
        self.dockerfiles_path.mkdir(parents=True, exist_ok=True)

    def create_dockerfile(self, service_name: str, config: Dict[str, Any]) -> Path:
        """Create Dockerfile for a service"""
        dockerfile_path = self.dockerfiles_path / f"Dockerfile.{service_name}"

        # Base template
        base_image = config.get("base_image", "python:3.9-slim")
        work_dir = config.get("work_dir", "/app")

        dockerfile_content = f"""
FROM {base_image}

WORKDIR {work_dir}

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    git \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements*.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set environment variables
ENV PYTHONPATH={work_dir}
ENV PYTHONUNBUFFERED=1

# Expose port
EXPOSE {config.get('port', 8000)}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{config.get('port', 8000)}/health || exit 1

# Default command
CMD {config.get('command', '["python", "-m", "src.main"]')}
        """.strip()

        with open(dockerfile_path, "w") as f:
            f.write(dockerfile_content)

        logger.info(f"Created Dockerfile for {service_name}")
        return dockerfile_path

    def create_docker_compose(self, services: Dict[str, Any]) -> Path:
        """Create docker-compose.yml for all services"""
        compose_config = {
            "version": "3.8",
            "services": {},
            "networks": {"ai-coding-network": {"driver": "bridge"}},
            "volumes": {"ai-coding-data": {"driver": "local"}},
        }

        for service_name, config in services.items():
            service_config = {
                "build": {
                    "context": ".",
                    "dockerfile": f"docker/Dockerfile.{service_name}",
                },
                "container_name": f"ai-coding-{service_name}",
                "restart": "unless-stopped",
                "environment": config.get("environment", {}),
                "ports": [
                    f"{config.get('host_port', 8000)}:{config.get('container_port', 8000)}"
                ],
                "volumes": [
                    "./data:/app/data",
                    "./logs:/app/logs",
                    "./config:/app/config",
                ],
                "networks": ["ai-coding-network"],
                "depends_on": config.get("depends_on", []),
            }

            # Add health check
            service_config["healthcheck"] = {
                "test": [
                    "CMD",
                    "curl",
                    "-f",
                    f"http://localhost:{config.get('container_port', 8000)}/health",
                ],
                "interval": "30s",
                "timeout": "10s",
                "retries": 3,
                "start_period": "40s",
            }

            compose_config["services"][service_name] = service_config

        with open(self.compose_path, "w") as f:
            yaml.dump(compose_config, f, default_flow_style=False)

        logger.info("Created docker-compose.yml")
        return self.compose_path

    def build_image(self, service_name: str) -> bool:
        """Build Docker image for a service"""
        try:
            cmd = [
                "docker",
                "build",
                "-f",
                f"docker/Dockerfile.{service_name}",
                "-t",
                f"ai-coding-{service_name}:latest",
                ".",
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"Successfully built image for {service_name}")
                return True
            else:
                logger.error(
                    f"Failed to build image for {service_name}: {result.stderr}"
                )
                return False

        except Exception as e:
            logger.error(f"Error building image: {e}")
            return False

    def run_container(self, service_name: str, detach: bool = True) -> bool:
        """Run container for a service"""
        try:
            cmd = [
                "docker",
                "run",
                "--name",
                f"ai-coding-{service_name}",
                "-d" if detach else "-it",
                "--network",
                "ai-coding-network",
                "-p",
                f"8000:8000",
                f"ai-coding-{service_name}:latest",
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"Successfully started container for {service_name}")
                return True
            else:
                logger.error(f"Failed to start container: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error running container: {e}")
            return False

    def stop_container(self, service_name: str) -> bool:
        """Stop and remove container"""
        try:
            # Stop container
            subprocess.run(
                ["docker", "stop", f"ai-coding-{service_name}"], capture_output=True
            )

            # Remove container
            subprocess.run(
                ["docker", "rm", f"ai-coding-{service_name}"], capture_output=True
            )

            logger.info(f"Stopped and removed container for {service_name}")
            return True

        except Exception as e:
            logger.error(f"Error stopping container: {e}")
            return False

    def get_container_status(self, service_name: str) -> Dict[str, Any]:
        """Get container status"""
        try:
            cmd = [
                "docker",
                "ps",
                "-f",
                f"name=ai-coding-{service_name}",
                "--format",
                "json",
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.stdout.strip():
                container_info = json.loads(result.stdout)
                return {
                    "status": "running",
                    "container_id": container_info.get("ID", ""),
                    "image": container_info.get("Image", ""),
                    "ports": container_info.get("Ports", ""),
                    "uptime": container_info.get("Status", ""),
                }
            else:
                return {"status": "not_running"}

        except Exception as e:
            logger.error(f"Error getting container status: {e}")
            return {"status": "error", "error": str(e)}

    def get_container_logs(self, service_name: str, tail: int = 100) -> str:
        """Get container logs"""
        try:
            cmd = ["docker", "logs", f"ai-coding-{service_name}", "--tail", str(tail)]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return result.stdout
            else:
                return f"Error getting logs: {result.stderr}"

        except Exception as e:
            return f"Error getting logs: {str(e)}"

    def deploy_stack(self) -> bool:
        """Deploy entire stack using docker-compose"""
        try:
            cmd = ["docker-compose", "up", "-d", "--build"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info("Successfully deployed stack")
                return True
            else:
                logger.error(f"Failed to deploy stack: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error deploying stack: {e}")
            return False

    def scale_service(self, service_name: str, replicas: int) -> bool:
        """Scale service to specified number of replicas"""
        try:
            cmd = [
                "docker-compose",
                "up",
                "-d",
                "--scale",
                f"{service_name}={replicas}",
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"Scaled {service_name} to {replicas} replicas")
                return True
            else:
                logger.error(f"Failed to scale service: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error scaling service: {e}")
            return False

    def cleanup(self) -> bool:
        """Clean up all containers and images"""
        try:
            # Stop all containers
            subprocess.run(["docker-compose", "down"], capture_output=True)

            # Remove unused images
            subprocess.run(["docker", "image", "prune", "-f"], capture_output=True)

            logger.info("Cleaned up containers and images")
            return True

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return False


# Global Docker manager instance
docker_manager = DockerManager()
