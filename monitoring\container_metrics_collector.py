# monitoring/container_metrics_collector.py
"""
Container Metrics Collector
Collects and analyzes Docker container performance metrics including CPU, memory, network, and disk usage.
Provides real-time monitoring and historical data collection for site containers.
"""

import asyncio
import json
import logging
import time
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

import docker
from docker import errors as docker_errors

logger = logging.getLogger(__name__)


@dataclass
class ContainerMetrics:
    """Container performance metrics data structure"""
    container_id: str
    container_name: str
    timestamp: datetime
    cpu_usage_percent: float
    memory_usage_mb: float
    memory_limit_mb: float
    memory_usage_percent: float
    network_rx_bytes: int
    network_tx_bytes: int
    disk_read_bytes: int
    disk_write_bytes: int
    status: str
    health_status: str = "unknown"
    uptime_seconds: int = 0
    restart_count: int = 0


@dataclass
class MetricsHistory:
    """Historical metrics data for a container"""
    container_name: str
    metrics: List[ContainerMetrics] = field(default_factory=list)
    max_history_size: int = 1000
    
    def add_metric(self, metric: ContainerMetrics):
        """Add a new metric, maintaining history size limit"""
        self.metrics.append(metric)
        if len(self.metrics) > self.max_history_size:
            self.metrics = self.metrics[-self.max_history_size:]
    
    def get_recent_metrics(self, minutes: int = 60) -> List[ContainerMetrics]:
        """Get metrics from the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [m for m in self.metrics if m.timestamp >= cutoff_time]
    
    def get_average_cpu(self, minutes: int = 60) -> float:
        """Get average CPU usage over the last N minutes"""
        recent = self.get_recent_metrics(minutes)
        if not recent:
            return 0.0
        return sum(m.cpu_usage_percent for m in recent) / len(recent)
    
    def get_average_memory(self, minutes: int = 60) -> float:
        """Get average memory usage over the last N minutes"""
        recent = self.get_recent_metrics(minutes)
        if not recent:
            return 0.0
        return sum(m.memory_usage_percent for m in recent) / len(recent)


class ContainerMetricsCollector:
    """
    Collects and manages Docker container performance metrics.
    Provides real-time monitoring and historical data analysis.
    """

    def __init__(self, collection_interval: int = 30, history_size: int = 1000):
        """Initialize metrics collector"""
        self.collection_interval = collection_interval
        self.history_size = history_size
        self.docker_client = docker.from_env()
        self.metrics_history: Dict[str, MetricsHistory] = {}
        self.is_collecting = False
        self.collection_task: Optional[asyncio.Task] = None
        self.metrics_file = Path("data/container_metrics.json")
        self.metrics_file.parent.mkdir(exist_ok=True)
        
        logger.info(f"Initialized ContainerMetricsCollector with {collection_interval}s interval")

    async def start_collection(self):
        """Start continuous metrics collection"""
        if self.is_collecting:
            logger.warning("Metrics collection already running")
            return
        
        self.is_collecting = True
        self.collection_task = asyncio.create_task(self._collection_loop())
        logger.info("Started container metrics collection")

    async def stop_collection(self):
        """Stop metrics collection"""
        if not self.is_collecting:
            return
        
        self.is_collecting = False
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Stopped container metrics collection")

    async def _collection_loop(self):
        """Main collection loop"""
        while self.is_collecting:
            try:
                await self.collect_all_metrics()
                await asyncio.sleep(self.collection_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(self.collection_interval)

    async def collect_all_metrics(self) -> Dict[str, ContainerMetrics]:
        """Collect metrics for all running site containers"""
        metrics = {}
        
        try:
            # Get all containers with site- prefix
            containers = self.docker_client.containers.list(
                filters={"name": "site-"}
            )
            
            for container in containers:
                try:
                    metric = await self._collect_container_metrics(container)
                    if metric:
                        metrics[container.name] = metric
                        
                        # Add to history
                        if container.name not in self.metrics_history:
                            self.metrics_history[container.name] = MetricsHistory(
                                container_name=container.name,
                                max_history_size=self.history_size
                            )
                        
                        self.metrics_history[container.name].add_metric(metric)
                        
                except Exception as e:
                    logger.error(f"Error collecting metrics for {container.name}: {e}")
            
            # Save metrics to file
            await self._save_metrics_to_file()
            
            logger.debug(f"Collected metrics for {len(metrics)} containers")
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting container metrics: {e}")
            return {}

    async def _collect_container_metrics(self, container) -> Optional[ContainerMetrics]:
        """Collect metrics for a single container"""
        try:
            # Get container stats
            stats = container.stats(stream=False)
            
            # Calculate CPU usage
            cpu_usage = self._calculate_cpu_usage(stats)
            
            # Calculate memory usage
            memory_usage_mb = stats['memory_stats']['usage'] / (1024 * 1024)
            memory_limit_mb = stats['memory_stats']['limit'] / (1024 * 1024)
            memory_usage_percent = (memory_usage_mb / memory_limit_mb) * 100
            
            # Calculate network usage
            network_stats = stats.get('networks', {})
            network_rx = sum(net.get('rx_bytes', 0) for net in network_stats.values())
            network_tx = sum(net.get('tx_bytes', 0) for net in network_stats.values())
            
            # Calculate disk usage
            disk_stats = stats.get('blkio_stats', {}).get('io_service_bytes_recursive', [])
            disk_read = sum(stat.get('value', 0) for stat in disk_stats if stat.get('op') == 'Read')
            disk_write = sum(stat.get('value', 0) for stat in disk_stats if stat.get('op') == 'Write')
            
            # Get container info
            container.reload()
            uptime = self._calculate_uptime(container)
            restart_count = container.attrs.get('RestartCount', 0)
            health_status = self._get_health_status(container)
            
            return ContainerMetrics(
                container_id=container.id[:12],
                container_name=container.name,
                timestamp=datetime.now(),
                cpu_usage_percent=cpu_usage,
                memory_usage_mb=memory_usage_mb,
                memory_limit_mb=memory_limit_mb,
                memory_usage_percent=memory_usage_percent,
                network_rx_bytes=network_rx,
                network_tx_bytes=network_tx,
                disk_read_bytes=disk_read,
                disk_write_bytes=disk_write,
                status=container.status,
                health_status=health_status,
                uptime_seconds=uptime,
                restart_count=restart_count
            )
            
        except Exception as e:
            logger.error(f"Error collecting metrics for container {container.name}: {e}")
            return None

    def _calculate_cpu_usage(self, stats: Dict[str, Any]) -> float:
        """Calculate CPU usage percentage from Docker stats"""
        try:
            cpu_stats = stats['cpu_stats']
            precpu_stats = stats['precpu_stats']
            
            cpu_delta = cpu_stats['cpu_usage']['total_usage'] - precpu_stats['cpu_usage']['total_usage']
            system_delta = cpu_stats['system_cpu_usage'] - precpu_stats['system_cpu_usage']
            
            if system_delta > 0 and cpu_delta > 0:
                cpu_usage = (cpu_delta / system_delta) * len(cpu_stats['cpu_usage']['percpu_usage']) * 100.0
                return round(cpu_usage, 2)
            
            return 0.0
            
        except (KeyError, ZeroDivisionError, TypeError):
            return 0.0

    def _calculate_uptime(self, container) -> int:
        """Calculate container uptime in seconds"""
        try:
            started_at = container.attrs['State']['StartedAt']
            if started_at:
                # Parse Docker timestamp format
                start_time = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
                uptime = datetime.now(start_time.tzinfo) - start_time
                return int(uptime.total_seconds())
            return 0
        except (KeyError, ValueError, TypeError):
            return 0

    def _get_health_status(self, container) -> str:
        """Get container health status"""
        try:
            health = container.attrs.get('State', {}).get('Health', {})
            return health.get('Status', 'unknown').lower()
        except (KeyError, AttributeError):
            return 'unknown'

    async def _save_metrics_to_file(self):
        """Save current metrics to JSON file"""
        try:
            # Prepare data for serialization
            data = {
                'timestamp': datetime.now().isoformat(),
                'containers': {}
            }
            
            for container_name, history in self.metrics_history.items():
                # Get last 100 metrics for file storage
                recent_metrics = history.metrics[-100:] if history.metrics else []
                data['containers'][container_name] = [
                    {
                        **asdict(metric),
                        'timestamp': metric.timestamp.isoformat()
                    }
                    for metric in recent_metrics
                ]
            
            with open(self.metrics_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving metrics to file: {e}")

    def get_container_metrics(self, container_name: str) -> Optional[MetricsHistory]:
        """Get metrics history for a specific container"""
        return self.metrics_history.get(container_name)

    def get_all_current_metrics(self) -> Dict[str, ContainerMetrics]:
        """Get the most recent metrics for all containers"""
        current_metrics = {}
        for container_name, history in self.metrics_history.items():
            if history.metrics:
                current_metrics[container_name] = history.metrics[-1]
        return current_metrics

    def get_performance_summary(self, container_name: str, minutes: int = 60) -> Dict[str, Any]:
        """Get performance summary for a container"""
        history = self.metrics_history.get(container_name)
        if not history:
            return {"error": f"No metrics found for container {container_name}"}
        
        recent_metrics = history.get_recent_metrics(minutes)
        if not recent_metrics:
            return {"error": f"No recent metrics found for container {container_name}"}
        
        latest = recent_metrics[-1]
        
        return {
            "container_name": container_name,
            "status": latest.status,
            "health_status": latest.health_status,
            "uptime_hours": latest.uptime_seconds / 3600,
            "restart_count": latest.restart_count,
            "current_cpu_percent": latest.cpu_usage_percent,
            "current_memory_percent": latest.memory_usage_percent,
            "current_memory_mb": latest.memory_usage_mb,
            "average_cpu_percent": history.get_average_cpu(minutes),
            "average_memory_percent": history.get_average_memory(minutes),
            "peak_cpu_percent": max(m.cpu_usage_percent for m in recent_metrics),
            "peak_memory_percent": max(m.memory_usage_percent for m in recent_metrics),
            "total_network_rx_mb": latest.network_rx_bytes / (1024 * 1024),
            "total_network_tx_mb": latest.network_tx_bytes / (1024 * 1024),
            "metrics_count": len(recent_metrics),
            "collection_period_minutes": minutes
        }

    async def cleanup_old_metrics(self, days: int = 7):
        """Clean up metrics older than specified days"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        for container_name, history in self.metrics_history.items():
            original_count = len(history.metrics)
            history.metrics = [m for m in history.metrics if m.timestamp >= cutoff_time]
            cleaned_count = original_count - len(history.metrics)
            
            if cleaned_count > 0:
                logger.info(f"Cleaned {cleaned_count} old metrics for {container_name}")
        
        logger.info(f"Completed metrics cleanup for data older than {days} days")
