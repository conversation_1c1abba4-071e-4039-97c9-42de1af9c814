#!/usr/bin/env python3
"""
Test script for cursor rules enforcement
"""

from core.cursor_rules_enforcer import CursorRulesEnforcer


def test_cursor_rules():
    """Test cursor rules enforcement"""
    print("🔍 Testing Cursor Rules Enforcement...")

    # Create enforcer
    enforcer = CursorRulesEnforcer()

    # Run compliance check
    result = enforcer.check_compliance()

    # Display results
    print(f"✅ Compliance Score: {result.get('compliance_score', 0):.1f}%")
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Critical Violations: {len(result.get('violations', []))}")
    print(f"Warnings: {len(result.get('warnings', []))}")

    # Show violations
    violations = result.get("violations", [])
    if violations:
        print("\n🚨 Critical Violations:")
        for i, violation in enumerate(violations, 1):
            print(f"  {i}. {violation}")
    else:
        print("\n✅ No critical violations found!")

    # Show warnings
    warnings = result.get("warnings", [])
    if warnings:
        print("\n⚠️ Warnings:")
        for i, warning in enumerate(warnings, 1):
            print(f"  {i}. {warning}")
    else:
        print("\n✅ No warnings found!")

    return result


if __name__ == "__main__":
    test_cursor_rules()
