# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.env.secrets
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts and cache
.ruff_cache/
.pytest_cache/
.swc/
.next/
coverage/
htmlcov/
.cache/
temp_rollback_tests/

# Project specific
logs/
*.log
backups/
*.zip
*.tar.gz
data/pipeline_state.json
data/*.db
data/*.sqlite
data/*.sqlite3

# Test reports and assets
test_reports/
test_assets/
test_assets_scan/
test_dist/
test_themes/
test_logs/
*.html

# Deployed sites (keep templates, exclude generated content)
sites/*/
!sites/templates/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Configuration files with sensitive data
config/*_secrets.json
config/*_private.json
.env.local
.env.production

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Node modules (if using any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test results and verification files
test_results_*.json
verification_results_*.json
logs/*.log
