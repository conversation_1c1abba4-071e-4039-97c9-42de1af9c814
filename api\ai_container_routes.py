# api/ai_container_routes.py
"""
AI Container Management API Routes
Provides REST API endpoints for AI-enhanced container management and optimization.
"""

from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from api.agent_dependency import get_agent
from core.ai_container_manager import AIEnhancedContainerManager

router = APIRouter(prefix="/api/ai-containers", tags=["AI Container Management"])


class AIAnalysisRequest(BaseModel):
    use_ai_optimization: bool = True
    analysis_depth: str = "standard"  # standard, deep, security-focused


class AIOptimizationRequest(BaseModel):
    optimization_type: str = "performance"  # performance, security, size, build-time
    apply_recommendations: bool = False


class DockerfileGenerationRequest(BaseModel):
    strategy: str = "auto"  # auto, multi-stage, minimal, security-focused
    include_security_scan: bool = True


class AIContainerResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    ai_confidence: Optional[float] = None
    error: Optional[str] = None


@router.post("/analyze/{site_name}", response_model=AIContainerResponse)
async def analyze_site_with_ai(
    site_name: str,
    request: AIAnalysisRequest,
    agent=Depends(get_agent)
):
    """Analyze a site using AI to provide optimization recommendations"""
    try:
        ai_manager = AIEnhancedContainerManager()
        
        if not ai_manager.ollama_available:
            return AIContainerResponse(
                success=False,
                message="AI analysis not available - Ollama not found or model not installed",
                error="AI service unavailable"
            )

        analysis = await ai_manager.analyze_and_optimize_site(site_name)

        if analysis["success"]:
            return AIContainerResponse(
                success=True,
                message=f"AI analysis completed for site {site_name}",
                data={
                    "site_name": site_name,
                    "framework": analysis.get("analysis", {}).get("framework", "unknown"),
                    "dockerfile_strategy": analysis.get("dockerfile_strategy", "standard"),
                    "security_recommendations": analysis.get("security_hardening", []),
                    "performance_optimizations": analysis.get("performance_optimizations", []),
                    "estimated_build_time": analysis.get("estimated_build_time", "unknown"),
                    "resource_recommendations": analysis.get("recommended_resources", {}),
                    "analysis_depth": request.analysis_depth
                },
                ai_confidence=analysis.get("ai_confidence", 0.0)
            )
        else:
            raise HTTPException(status_code=400, detail=analysis["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/optimize/{site_name}", response_model=AIContainerResponse)
async def optimize_site_container(
    site_name: str,
    request: AIOptimizationRequest,
    agent=Depends(get_agent)
):
    """Optimize a site container using AI recommendations"""
    try:
        ai_manager = AIEnhancedContainerManager()
        
        if not ai_manager.ollama_available:
            return AIContainerResponse(
                success=False,
                message="AI optimization not available - Ollama not found or model not installed",
                error="AI service unavailable"
            )

        # Get AI analysis first
        analysis = await ai_manager.analyze_and_optimize_site(site_name)
        
        if not analysis["success"]:
            raise HTTPException(status_code=400, detail=analysis["error"])

        optimization_data = {
            "site_name": site_name,
            "optimization_type": request.optimization_type,
            "recommendations": [],
            "applied_optimizations": []
        }

        # Extract relevant optimizations based on type
        if request.optimization_type == "performance":
            optimization_data["recommendations"] = analysis.get("performance_optimizations", [])
        elif request.optimization_type == "security":
            optimization_data["recommendations"] = analysis.get("security_hardening", [])
        elif request.optimization_type == "size":
            optimization_data["recommendations"] = [
                "Use multi-stage builds",
                "Minimize layer count",
                "Use alpine base images where possible"
            ]
        elif request.optimization_type == "build-time":
            optimization_data["recommendations"] = [
                "Optimize dependency caching",
                "Use .dockerignore effectively",
                "Parallelize build steps"
            ]

        # Apply optimizations if requested
        if request.apply_recommendations:
            site_config = {"name": site_name, "optimization_type": request.optimization_type}
            result = await ai_manager.create_optimized_site_container(
                site_name=site_name,
                config=site_config,
                use_ai_optimization=True
            )
            
            if result["success"]:
                optimization_data["applied_optimizations"] = optimization_data["recommendations"]
                optimization_data["container_created"] = True
                optimization_data["port"] = result.get("port")
            else:
                optimization_data["application_error"] = result.get("error")

        return AIContainerResponse(
            success=True,
            message=f"AI optimization completed for site {site_name}",
            data=optimization_data,
            ai_confidence=analysis.get("ai_confidence", 0.0)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/confidence/{site_name}", response_model=AIContainerResponse)
async def get_ai_confidence_score(site_name: str, agent=Depends(get_agent)):
    """Get AI confidence score for site analysis and recommendations"""
    try:
        ai_manager = AIEnhancedContainerManager()
        
        if not ai_manager.ollama_available:
            return AIContainerResponse(
                success=False,
                message="AI confidence scoring not available - Ollama not found or model not installed",
                error="AI service unavailable"
            )

        analysis = await ai_manager.analyze_and_optimize_site(site_name)

        if analysis["success"]:
            confidence_data = {
                "site_name": site_name,
                "overall_confidence": analysis.get("ai_confidence", 0.0),
                "analysis_quality": "high" if analysis.get("ai_confidence", 0.0) > 0.8 else "medium" if analysis.get("ai_confidence", 0.0) > 0.6 else "low",
                "recommendation_reliability": analysis.get("ai_confidence", 0.0),
                "factors_affecting_confidence": [
                    "Site complexity",
                    "Framework detection accuracy",
                    "Available code patterns",
                    "Security analysis depth"
                ]
            }

            return AIContainerResponse(
                success=True,
                message=f"AI confidence score retrieved for site {site_name}",
                data=confidence_data,
                ai_confidence=analysis.get("ai_confidence", 0.0)
            )
        else:
            raise HTTPException(status_code=400, detail=analysis["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-dockerfile/{site_name}", response_model=AIContainerResponse)
async def generate_intelligent_dockerfile(
    site_name: str,
    request: DockerfileGenerationRequest,
    agent=Depends(get_agent)
):
    """Generate an intelligent Dockerfile using AI analysis"""
    try:
        ai_manager = AIEnhancedContainerManager()
        
        if not ai_manager.ollama_available:
            return AIContainerResponse(
                success=False,
                message="AI Dockerfile generation not available - Ollama not found or model not installed",
                error="AI service unavailable"
            )

        result = await ai_manager.generate_intelligent_dockerfile(
            site_name=site_name,
            strategy=request.strategy
        )

        if result["success"]:
            dockerfile_data = {
                "site_name": site_name,
                "dockerfile_content": result["dockerfile_content"],
                "dockerignore_content": result["dockerignore_content"],
                "strategy_used": result["strategy_used"],
                "optimizations_applied": result["optimizations_applied"],
                "estimated_build_time": result["estimated_build_time"],
                "include_security_scan": request.include_security_scan
            }

            if request.include_security_scan:
                dockerfile_data["security_scan"] = result.get("security_scan", {})

            return AIContainerResponse(
                success=True,
                message=f"Intelligent Dockerfile generated for site {site_name}",
                data=dockerfile_data,
                ai_confidence=result.get("ai_confidence", 0.0)
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=AIContainerResponse)
async def get_ai_service_status(agent=Depends(get_agent)):
    """Get the status of AI container management services"""
    try:
        ai_manager = AIEnhancedContainerManager()
        
        status_data = {
            "ollama_available": ai_manager.ollama_available,
            "model": ai_manager.ollama_model,
            "ai_features_enabled": ai_manager.ollama_available,
            "supported_operations": [
                "site_analysis",
                "optimization_recommendations", 
                "dockerfile_generation",
                "security_analysis",
                "confidence_scoring"
            ] if ai_manager.ollama_available else []
        }

        return AIContainerResponse(
            success=True,
            message="AI service status retrieved",
            data=status_data,
            ai_confidence=1.0 if ai_manager.ollama_available else 0.0
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
