# core/ai_container_manager.py
"""
AI-Enhanced Container Manager
Extends SiteContainerManager with AI capabilities for intelligent site analysis and optimization.
Uses local Ollama models for code analysis and optimization suggestions.
"""

import asyncio
import json
import logging
import subprocess
from pathlib import Path
from typing import Any, Dict, List

from core.site_container_manager import SiteContainerManager

logger = logging.getLogger(__name__)


class AIEnhancedContainerManager(SiteContainerManager):
    """
    AI-Enhanced Container Manager that extends SiteContainerManager with AI capabilities.
    Uses local Ollama models for intelligent site analysis and optimization.
    """

    def __init__(self, ollama_model: str = "deepseek-coder:1.3b"):
        """Initialize AI-Enhanced Container Manager with specified Ollama model"""
        super().__init__()
        self.ollama_model = ollama_model
        self.ollama_available = self._check_ollama_availability()

        if not self.ollama_available:
            logger.warning("Ollama not available - AI features will be disabled")

    def _check_ollama_availability(self) -> bool:
        """Check if Ollama is available and the specified model is installed"""
        try:
            # Check if ollama command is available
            result = subprocess.run(
                ["ollama", "list"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                return False

            # Check if our model is available
            models = result.stdout
            return self.ollama_model in models

        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False

    async def analyze_and_optimize_site(self, site_name: str) -> Dict[str, Any]:
        """Use AI to analyze site and suggest optimizations"""
        if not self.ollama_available:
            return {
                "success": False,
                "error": "AI analysis not available - Ollama not found or model not installed"
            }

        try:
            site_path = self.sites_dir / site_name
            if not site_path.exists():
                return {
                    "success": False,
                    "error": f"Site {site_name} not found"
                }

            # Analyze site structure
            site_analysis = await self._ai_analyze_site_structure(site_path)

            # Generate optimization recommendations
            optimizations = await self._ai_generate_optimizations(site_analysis)

            # Analyze security implications
            security_analysis = await self._ai_analyze_security(site_path)

            return {
                "success": True,
                "site_name": site_name,
                "analysis": site_analysis,
                "dockerfile_strategy": optimizations.get("dockerfile_strategy", "standard"),
                "security_hardening": security_analysis.get("recommendations", []),
                "performance_optimizations": optimizations.get("performance_tips", []),
                "estimated_build_time": optimizations.get("build_time_estimate", "unknown"),
                "recommended_resources": optimizations.get("resource_recommendations", {}),
                "ai_confidence": optimizations.get("confidence_score", 0.0)
            }

        except Exception as e:
            logger.error(f"AI analysis failed for {site_name}: {e}")
            return {
                "success": False,
                "error": f"AI analysis failed: {str(e)}"
            }

    async def _ai_analyze_site_structure(self, site_path: Path) -> Dict[str, Any]:
        """Use AI to analyze site structure and characteristics"""
        # Get basic characteristics first
        characteristics = {
            "framework": self._detect_site_framework(site_path),
            "needs_build": False,
            "build_output_dir": None,
            "runtime_files": [],
            "dev_files": [],
            "package_manager": None,
            "has_dependencies": False
        }

        # Enhanced detection logic
        if (site_path / "package.json").exists():
            characteristics["has_dependencies"] = True
            characteristics["package_manager"] = "npm"
        elif (site_path / "requirements.txt").exists():
            characteristics["has_dependencies"] = True
            characteristics["package_manager"] = "pip"

        # Prepare site information for AI analysis
        site_info = self._gather_site_information(site_path)

        # Create AI prompt for analysis
        prompt = self._create_analysis_prompt(site_info, characteristics)

        # Query Ollama for analysis
        ai_response = await self._query_ollama(prompt)

        # Parse and validate AI response
        analysis = self._parse_ai_analysis(ai_response)

        # Merge with basic characteristics
        analysis.update(characteristics)

        return analysis

    async def _ai_generate_optimizations(self, site_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate optimization recommendations using AI"""
        prompt = f"""
        Analyze this website structure and provide Docker optimization recommendations:

        Framework: {site_analysis.get('framework', 'unknown')}
        Dependencies: {site_analysis.get('has_dependencies', False)}
        Build Required: {site_analysis.get('needs_build', False)}
        Package Manager: {site_analysis.get('package_manager', 'none')}

        Provide recommendations for:
        1. Dockerfile optimization strategy
        2. Performance improvements
        3. Build time reduction
        4. Resource requirements

        Respond in JSON format with keys: dockerfile_strategy, performance_tips, build_time_estimate, resource_recommendations, confidence_score
        """

        ai_response = await self._query_ollama(prompt)
        return self._parse_optimization_response(ai_response)

    async def _ai_analyze_security(self, site_path: Path) -> Dict[str, Any]:
        """Analyze security implications and provide recommendations"""
        # Scan for common security files and patterns
        security_files = self._scan_security_files(site_path)

        prompt = f"""
        Analyze this website for security considerations in Docker containerization:

        Security files found: {security_files}

        Provide recommendations for:
        1. Container security hardening
        2. Secrets management
        3. Network security
        4. File permissions

        Respond in JSON format with key: recommendations (array of strings)
        """

        ai_response = await self._query_ollama(prompt)
        return self._parse_security_response(ai_response)

    def _gather_site_information(self, site_path: Path) -> Dict[str, Any]:
        """Gather comprehensive site information for AI analysis"""
        info = {
            "files": [],
            "directories": [],
            "config_files": [],
            "dependencies": {},
            "file_count": 0,
            "total_size": 0
        }

        try:
            for item in site_path.rglob("*"):
                if item.is_file():
                    info["files"].append(item.name)
                    info["file_count"] += 1
                    try:
                        info["total_size"] += item.stat().st_size
                    except (OSError, PermissionError):
                        pass

                    # Check for config files
                    if item.name in ["package.json", "requirements.txt", "Dockerfile", "docker-compose.yml", ".env"]:
                        info["config_files"].append(item.name)

                elif item.is_dir():
                    info["directories"].append(item.name)

            # Analyze package.json if present
            package_json = site_path / "package.json"
            if package_json.exists():
                try:
                    with open(package_json, "r") as f:
                        package_data = json.load(f)
                        info["dependencies"]["npm"] = {
                            "dependencies": len(package_data.get("dependencies", {})),
                            "devDependencies": len(package_data.get("devDependencies", {})),
                            "scripts": list(package_data.get("scripts", {}).keys())
                        }
                except (json.JSONDecodeError, FileNotFoundError):
                    pass

            # Analyze requirements.txt if present
            requirements_txt = site_path / "requirements.txt"
            if requirements_txt.exists():
                try:
                    with open(requirements_txt, "r") as f:
                        requirements = f.readlines()
                        info["dependencies"]["pip"] = {
                            "count": len([r for r in requirements if r.strip() and not r.startswith("#")]),
                            "requirements": [r.strip() for r in requirements[:10]]  # First 10 for analysis
                        }
                except FileNotFoundError:
                    pass

        except Exception as e:
            logger.warning(f"Error gathering site information: {e}")

        return info

    def _create_analysis_prompt(self, site_info: Dict[str, Any], characteristics: Dict[str, Any]) -> str:
        """Create AI prompt for site analysis"""
        return f"""
        Analyze this website structure for Docker containerization:

        Basic Info:
        - Framework: {characteristics.get('framework', 'unknown')}
        - File count: {site_info.get('file_count', 0)}
        - Total size: {site_info.get('total_size', 0)} bytes
        - Config files: {site_info.get('config_files', [])}

        Dependencies:
        {json.dumps(site_info.get('dependencies', {}), indent=2)}

        Provide analysis in JSON format with keys:
        - complexity_score (0-10)
        - build_complexity (low/medium/high)
        - optimization_potential (low/medium/high)
        - recommended_strategy (single-stage/multi-stage/custom)
        """

    async def _query_ollama(self, prompt: str) -> str:
        """Query Ollama model with the given prompt"""
        if not self.ollama_available:
            return "{}"

        try:
            process = await asyncio.create_subprocess_exec(
                "ollama", "run", self.ollama_model,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate(prompt.encode())

            if process.returncode != 0:
                logger.error(f"Ollama query failed: {stderr.decode()}")
                return "{}"

            return stdout.decode().strip()

        except Exception as e:
            logger.error(f"Error querying Ollama: {e}")
            return "{}"

    def _parse_ai_analysis(self, ai_response: str) -> Dict[str, Any]:
        """Parse AI analysis response"""
        try:
            return json.loads(ai_response)
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI analysis response as JSON")
            return {
                "complexity_score": 5,
                "build_complexity": "medium",
                "optimization_potential": "medium",
                "recommended_strategy": "single-stage"
            }

    def _parse_optimization_response(self, ai_response: str) -> Dict[str, Any]:
        """Parse AI optimization response"""
        try:
            return json.loads(ai_response)
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI optimization response as JSON")
            return {
                "dockerfile_strategy": "standard",
                "performance_tips": ["Use multi-stage builds", "Minimize layer count"],
                "build_time_estimate": "2-5 minutes",
                "resource_recommendations": {"cpu": "0.5", "memory": "256M"},
                "confidence_score": 0.5
            }

    def _parse_security_response(self, ai_response: str) -> Dict[str, Any]:
        """Parse AI security response"""
        try:
            return json.loads(ai_response)
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI security response as JSON")
            return {
                "recommendations": [
                    "Use non-root user in container",
                    "Pin base image versions",
                    "Scan for vulnerabilities",
                    "Minimize attack surface"
                ]
            }

    def _scan_security_files(self, site_path: Path) -> List[str]:
        """Scan for security-related files"""
        security_files = []
        security_patterns = [
            ".env", ".env.example", "secrets.json", "config.json",
            "ssl", "cert", "key", "auth", "password", "token"
        ]

        try:
            for item in site_path.rglob("*"):
                if item.is_file():
                    filename = item.name.lower()
                    if any(pattern in filename for pattern in security_patterns):
                        security_files.append(item.name)
        except Exception as e:
            logger.warning(f"Error scanning security files: {e}")

        return security_files

    def _detect_site_framework(self, site_path: Path) -> str:
        """Detect the framework used by the site"""
        if (site_path / "package.json").exists():
            try:
                with open(site_path / "package.json", "r") as f:
                    package_data = json.load(f)
                    dependencies = package_data.get("dependencies", {})

                    if "next" in dependencies:
                        return "nextjs"
                    elif "react" in dependencies:
                        return "react"
                    elif "vue" in dependencies:
                        return "vue"
                    else:
                        return "node"
            except (json.JSONDecodeError, FileNotFoundError):
                pass

        if (site_path / "requirements.txt").exists():
            try:
                with open(site_path / "requirements.txt", "r") as f:
                    requirements = f.read().lower()
                    if "flask" in requirements:
                        return "flask"
                    elif "django" in requirements:
                        return "django"
                    elif "fastapi" in requirements:
                        return "fastapi"
                    else:
                        return "python"
            except FileNotFoundError:
                pass

        # Check for static files
        static_files = list(site_path.glob("*.html"))
        if static_files:
            return "static"

        return "unknown"

    async def create_optimized_site_container(
        self,
        site_name: str,
        config: Dict[str, Any],
        use_ai_optimization: bool = True
    ) -> Dict[str, Any]:
        """Create site container with optional AI optimization"""

        if use_ai_optimization and self.ollama_available:
            # Get AI analysis first
            analysis = await self.analyze_and_optimize_site(site_name)

            if analysis["success"]:
                # Apply AI recommendations to config
                config = self._apply_ai_recommendations(config, analysis)
                logger.info(f"Applied AI optimizations for {site_name}")
            else:
                logger.warning(f"AI optimization failed for {site_name}, using standard approach")

        # Create container using parent class method
        return await self.create_site_container(site_name, config)

    def _apply_ai_recommendations(self, config: Dict[str, Any], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Apply AI recommendations to container configuration"""
        optimized_config = config.copy()

        # Apply resource recommendations
        if "recommended_resources" in analysis:
            resources = analysis["recommended_resources"]
            optimized_config["resources"] = {
                "limits": {
                    "cpus": resources.get("cpu", "1.0"),
                    "memory": resources.get("memory", "512M")
                }
            }

        # Apply dockerfile strategy
        dockerfile_strategy = analysis.get("dockerfile_strategy", "standard")
        optimized_config["dockerfile_strategy"] = dockerfile_strategy

        # Apply security recommendations
        if "security_hardening" in analysis:
            optimized_config["security_features"] = analysis["security_hardening"]

        return optimized_config

    async def generate_intelligent_dockerfile(self, site_name: str, strategy: str = "auto") -> Dict[str, Any]:
        """Generate intelligent Dockerfile based on AI analysis"""
        try:
            site_path = self.sites_dir / site_name
            if not site_path.exists():
                return {"success": False, "error": f"Site {site_name} not found"}

            # Get AI analysis for intelligent generation
            analysis = await self.analyze_and_optimize_site(site_name)

            if not analysis["success"]:
                return {"success": False, "error": "AI analysis failed for Dockerfile generation"}

            # Determine optimal Dockerfile strategy
            if strategy == "auto":
                strategy = analysis.get("dockerfile_strategy", "standard")

            # Generate Dockerfile based on strategy and analysis
            dockerfile_content = await self._generate_optimized_dockerfile(
                site_path, analysis, strategy
            )

            # Perform security scanning on generated Dockerfile
            security_scan = await self._scan_dockerfile_security(dockerfile_content)

            # Generate optimized .dockerignore
            dockerignore_content = self._generate_intelligent_dockerignore(
                site_path, analysis
            )

            return {
                "success": True,
                "dockerfile_content": dockerfile_content,
                "dockerignore_content": dockerignore_content,
                "strategy_used": strategy,
                "security_scan": security_scan,
                "optimizations_applied": analysis.get("performance_optimizations", []),
                "estimated_build_time": analysis.get("estimated_build_time", "unknown"),
                "ai_confidence": analysis.get("ai_confidence", 0.0)
            }

        except Exception as e:
            logger.error(f"Error generating intelligent Dockerfile: {e}")
            return {"success": False, "error": str(e)}

    async def _generate_optimized_dockerfile(
        self, site_path: Path, analysis: Dict[str, Any], strategy: str
    ) -> str:
        """Generate optimized Dockerfile based on analysis and strategy"""

        framework = analysis.get("analysis", {}).get("framework", "static")
        logger.debug(f"Generating Dockerfile for {site_path.name} with strategy {strategy}")

        # Base image selection with security considerations
        base_images = {
            "python": "python:3.11-slim@sha256:f2ee145f3bc4e061f8dfe7e6ebd427a410121495a0bd26e7622136db060b7e8e",
            "node": "node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d",
            "nginx": "nginx:1.25-alpine@sha256:2d194184b067db3598771b4cf326cfe6ad5051937ba2cc83222d94d4cd9c81bd"
        }

        if strategy == "multi-stage" and framework in ["react", "nextjs", "vue"]:
            return self._generate_multistage_dockerfile(framework, base_images, analysis)
        elif framework in ["python", "flask", "django", "fastapi"]:
            return self._generate_python_dockerfile(base_images["python"], analysis)
        elif framework in ["react", "nextjs", "vue"]:
            return self._generate_node_dockerfile(framework, base_images, analysis)
        else:
            return self._generate_static_dockerfile(base_images["nginx"], analysis)

    def _generate_multistage_dockerfile(
        self, framework: str, base_images: Dict[str, str], analysis: Dict[str, Any]
    ) -> str:
        """Generate multi-stage Dockerfile for Node.js frameworks"""

        build_optimizations = analysis.get("performance_optimizations", [])
        use_cache_mount = "cache-optimization" in build_optimizations

        dockerfile = f"""# AI-Generated Multi-Stage Dockerfile for {framework}
# Generated with AI confidence: {analysis.get('ai_confidence', 0.0):.2f}

# Build stage
FROM {base_images['node']} AS builder

# Create non-root user early
RUN addgroup --system --gid 1000 appuser && \\
    adduser --system --uid 1000 --ingroup appuser appuser

WORKDIR /app

# Copy package files for dependency caching
COPY --chown=appuser:appuser package*.json ./

# Install dependencies with optimization
RUN npm ci --only=production --no-audit --no-fund"""

        if use_cache_mount:
            dockerfile += " --cache /tmp/.npm"

        dockerfile += f"""

# Copy source code
COPY --chown=appuser:appuser . .

# Build application
RUN npm run build

# Production stage
FROM {base_images['nginx']} AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup --system --gid 1000 appuser && \\
    adduser --system --uid 1000 --ingroup appuser appuser

# Copy built application from builder stage
COPY --from=builder --chown=appuser:appuser /app/build /usr/share/nginx/html/

# Configure nginx for non-root
RUN chown -R appuser:appuser /var/cache/nginx /var/log/nginx /etc/nginx/conf.d \\
    && touch /var/run/nginx.pid \\
    && chown appuser:appuser /var/run/nginx.pid

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
"""
        return dockerfile

    def _generate_python_dockerfile(self, base_image: str, analysis: Dict[str, Any]) -> str:
        """Generate optimized Python Dockerfile"""

        security_features = analysis.get("security_hardening", [])
        use_security_scan = "vulnerability-scanning" in security_features

        dockerfile = f"""# AI-Generated Python Dockerfile
# Generated with AI confidence: {analysis.get('ai_confidence', 0.0):.2f}

FROM {base_image}

# Create non-root user early
RUN groupadd --gid 1000 appuser && \\
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*"""

        if use_security_scan:
            dockerfile += """

# Install security scanning tools
RUN pip install --no-cache-dir safety bandit"""

        dockerfile += """

# Copy requirements first for better caching
COPY --chown=appuser:appuser requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt"""

        if use_security_scan:
            dockerfile += """

# Run security scans
RUN safety check --json || true
RUN bandit -r . -f json || true"""

        dockerfile += """

# Copy application code
COPY --chown=appuser:appuser . .

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

# Start application
CMD ["python", "-m", "http.server", "80", "--bind", "0.0.0.0"]
"""
        return dockerfile

    def _generate_node_dockerfile(
        self, framework: str, base_images: Dict[str, str], analysis: Dict[str, Any]
    ) -> str:
        """Generate optimized Node.js Dockerfile"""

        dockerfile = f"""# AI-Generated {framework} Dockerfile
# Generated with AI confidence: {analysis.get('ai_confidence', 0.0):.2f}

FROM {base_images['node']}

# Create non-root user early
RUN addgroup --system --gid 1000 appuser && \\
    adduser --system --uid 1000 --ingroup appuser appuser

WORKDIR /app

# Copy package files for dependency caching
COPY --chown=appuser:appuser package*.json ./

# Install dependencies
RUN npm ci --only=production --no-audit --no-fund

# Copy application code
COPY --chown=appuser:appuser . .

# Build if needed
RUN if [ -f "package.json" ] && npm run build 2>/dev/null; then echo "Build completed"; fi

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

# Start application
CMD ["npm", "start"]
"""
        return dockerfile

    def _generate_static_dockerfile(self, base_image: str, analysis: Dict[str, Any]) -> str:
        """Generate optimized static site Dockerfile"""

        dockerfile = f"""# AI-Generated Static Site Dockerfile
# Generated with AI confidence: {analysis.get('ai_confidence', 0.0):.2f}

FROM {base_image}

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup --system --gid 1000 appuser && \\
    adduser --system --uid 1000 --ingroup appuser appuser

# Copy site files with proper ownership
COPY --chown=appuser:appuser . /usr/share/nginx/html/

# Configure nginx for non-root
RUN chown -R appuser:appuser /var/cache/nginx /var/log/nginx /etc/nginx/conf.d \\
    && touch /var/run/nginx.pid \\
    && chown appuser:appuser /var/run/nginx.pid

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
"""
        return dockerfile

    async def _scan_dockerfile_security(self, dockerfile_content: str) -> Dict[str, Any]:
        """Scan Dockerfile for security issues"""
        security_issues = []
        recommendations = []

        lines = dockerfile_content.split('\n')

        for i, line in enumerate(lines, 1):
            line = line.strip()

            # Check for security best practices
            if line.startswith('FROM') and ':latest' in line:
                security_issues.append({
                    "line": i,
                    "issue": "Using 'latest' tag",
                    "severity": "medium",
                    "recommendation": "Pin to specific version"
                })

            if line.startswith('RUN') and 'sudo' in line:
                security_issues.append({
                    "line": i,
                    "issue": "Using sudo in container",
                    "severity": "high",
                    "recommendation": "Avoid sudo, use proper user management"
                })

            if 'USER root' in line:
                security_issues.append({
                    "line": i,
                    "issue": "Running as root user",
                    "severity": "high",
                    "recommendation": "Use non-root user"
                })

        # Generate recommendations
        if not any('USER' in line for line in lines):
            recommendations.append("Add non-root user for security")

        if not any('HEALTHCHECK' in line for line in lines):
            recommendations.append("Add health check for monitoring")

        return {
            "security_issues": security_issues,
            "recommendations": recommendations,
            "security_score": max(0, 100 - len(security_issues) * 10),
            "scan_timestamp": "2024-01-01T00:00:00Z"  # Would use actual timestamp
        }

    def _generate_intelligent_dockerignore(
        self, site_path: Path, analysis: Dict[str, Any]
    ) -> str:
        """Generate intelligent .dockerignore based on analysis"""

        framework = analysis.get("analysis", {}).get("framework", "static")
        logger.debug(f"Generating .dockerignore for {site_path.name} framework: {framework}")

        ignore_patterns = [
            "# AI-Generated .dockerignore",
            "# Common patterns",
            ".git/", ".vscode/", ".idea/",
            "*.log", "*.tmp", ".DS_Store",
            "README.md", "*.md", "docs/",
            ".env*", "!.env.example"
        ]

        # Framework-specific patterns
        if framework in ["react", "nextjs", "vue"]:
            ignore_patterns.extend([
                "# Node.js patterns",
                "node_modules/", "npm-debug.log*",
                ".next/", "out/", "build/", "dist/",
                "coverage/", ".nyc_output/",
                ".cache/", ".parcel-cache/"
            ])

        if framework in ["python", "flask", "django", "fastapi"]:
            ignore_patterns.extend([
                "# Python patterns",
                "__pycache__/", "*.pyc", "*.pyo",
                ".pytest_cache/", "coverage.xml",
                "venv/", ".venv/", "env/",
                "*.egg-info/", "dist/", "build/"
            ])

        # Add test patterns
        ignore_patterns.extend([
            "# Test patterns",
            "tests/", "__tests__/", "*.test.*",
            "*.spec.*", "test_*.py"
        ])

        return '\n'.join(ignore_patterns)
