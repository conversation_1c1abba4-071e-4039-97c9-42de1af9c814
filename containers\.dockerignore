# Container-specific .dockerignore
# Optimizes container builds by excluding unnecessary files

# Git and version control
.git
.gitignore
.gitattributes

# Documentation
*.md
README*
docs/

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.pytest_cache/

# Virtual environments
.venv/
venv/
env/

# Logs and temporary files
logs/
*.log
*.tmp
*.temp
temp/
tmp/

# Data and cache directories
data/
cache/
.cache/

# Test files and reports
tests/
test_*
*_test.py
test_reports/
coverage/
.coverage

# Build artifacts
build/
dist/
*.egg-info/

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/

# Environment files (keep examples)
.env*
!.env.example
!.env.secrets.example

# Backup files
*.bak
*.backup
backups/

# Large binary files
*.zip
*.tar.gz
*.rar
*.7z

# Model files (if not needed in container)
models/trained/
*.bin
*.safetensors

# Secrets (now externalized)
secrets/

# Container states (regenerated)
container_states.json
enhanced_container_states.json
port_registry.json

# Extracted configuration files
extracted/
