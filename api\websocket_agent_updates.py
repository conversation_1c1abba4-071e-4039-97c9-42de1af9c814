import asyncio
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Set

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, WebSocket, WebSocketDisconnect

from core.agents.agent_message_bus import (
    AgentMessage,
    AgentMessageBus,
    MessagePriority,
    MessageType,
)

logger = logging.getLogger(__name__)


class WebSocketConnectionManager:
    """Manages WebSocket connections for real-time agent updates."""

    def __init__(self, message_bus: AgentMessageBus):
        self.message_bus = message_bus
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        # agent_id -> connection_ids
        self.agent_connections: Dict[str, Set[str]] = {}

        # Register with message bus for notifications
        self.message_bus.add_websocket_callback(self._on_agent_message)

        logger.info("WebSocketConnectionManager initialized")

    async def connect(
        self, websocket: WebSocket, connection_id: str, agent_id: Optional[str] = None
    ) -> None:
        """Connect a new WebSocket client."""
        await websocket.accept()

        self.active_connections[connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "agent_id": agent_id,
            "connected_at": datetime.now().isoformat(),
            "message_count": 0,
            "last_activity": datetime.now().isoformat(),
        }

        # Track agent connections
        if agent_id:
            if agent_id not in self.agent_connections:
                self.agent_connections[agent_id] = set()
            self.agent_connections[agent_id].add(connection_id)

        # Register with message bus
        self.message_bus.add_websocket_client(connection_id, websocket)

        # Send welcome message
        await self._send_to_connection(
            connection_id,
            {
                "type": "connection_established",
                "connection_id": connection_id,
                "agent_id": agent_id,
                "timestamp": datetime.now().isoformat(),
            },
        )

        logger.info(f"WebSocket client {connection_id} connected (agent: {agent_id})")

    async def disconnect(self, connection_id: str) -> None:
        """Disconnect a WebSocket client."""
        if connection_id not in self.active_connections:
            return

        # Get agent ID before cleanup
        agent_id = self.connection_metadata.get(connection_id, {}).get("agent_id")

        # Clean up connections
        del self.active_connections[connection_id]
        del self.connection_metadata[connection_id]

        # Clean up agent connections
        if agent_id and agent_id in self.agent_connections:
            self.agent_connections[agent_id].discard(connection_id)
            if not self.agent_connections[agent_id]:
                del self.agent_connections[agent_id]

        # Remove from message bus
        self.message_bus.remove_websocket_client(connection_id)

        logger.info(f"WebSocket client {connection_id} disconnected")

    async def _send_to_connection(
        self, connection_id: str, data: Dict[str, Any]
    ) -> bool:
        """Send data to a specific connection."""
        if connection_id not in self.active_connections:
            return False

        try:
            websocket = self.active_connections[connection_id]
            await websocket.send_text(json.dumps(data))

            # Update metadata
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["message_count"] += 1
                self.connection_metadata[connection_id][
                    "last_activity"
                ] = datetime.now().isoformat()

            return True
        except Exception as e:
            logger.error(f"Error sending to connection {connection_id}: {e}")
            # Mark for cleanup
            await self.disconnect(connection_id)
            return False

    async def send_to_agent(self, agent_id: str, data: Dict[str, Any]) -> int:
        """Send data to all connections for a specific agent."""
        if agent_id not in self.agent_connections:
            return 0

        sent_count = 0
        connection_ids = list(self.agent_connections[agent_id])

        for connection_id in connection_ids:
            success = await self._send_to_connection(connection_id, data)
            if success:
                sent_count += 1

        return sent_count

    async def broadcast(
        self, data: Dict[str, Any], exclude_connections: Optional[List[str]] = None
    ) -> int:
        """Broadcast data to all connected clients."""
        exclude_connections = exclude_connections or []
        sent_count = 0

        connection_ids = [
            conn_id
            for conn_id in self.active_connections.keys()
            if conn_id not in exclude_connections
        ]

        for connection_id in connection_ids:
            success = await self._send_to_connection(connection_id, data)
            if success:
                sent_count += 1

        return sent_count

    def _on_agent_message(self, message: AgentMessage) -> None:
        """Handle agent message notifications."""
        try:
            # Create WebSocket notification
            notification = {
                "type": "agent_message",
                "message": message.to_dict(),
                "timestamp": datetime.now().isoformat(),
            }

            # Send to relevant connections
            asyncio.create_task(
                self._handle_message_notification(message, notification)
            )
        except Exception as e:
            logger.error(f"Error handling agent message notification: {e}")

    async def _handle_message_notification(
        self, message: AgentMessage, notification: Dict[str, Any]
    ) -> None:
        """Handle message notification routing."""
        try:
            # Send to recipient if direct message
            if message.recipient_id:
                await self.send_to_agent(message.recipient_id, notification)

            # Send to sender for confirmation
            await self.send_to_agent(
                message.sender_id, {**notification, "type": "message_sent"}
            )

            # Broadcast high-priority or system messages
            if (
                message.priority.value >= MessagePriority.HIGH.value
                or message.message_type == MessageType.SYSTEM_NOTIFICATION
            ):
                await self.broadcast(notification)
        except Exception as e:
            logger.error(f"Error routing message notification: {e}")

    async def handle_client_message(
        self, connection_id: str, data: Dict[str, Any]
    ) -> None:
        """Handle incoming messages from WebSocket clients."""
        try:
            message_type = data.get("type")
            agent_id = self.connection_metadata.get(connection_id, {}).get("agent_id")

            if message_type == "send_agent_message":
                await self._handle_send_message_request(connection_id, agent_id, data)
            elif message_type == "subscribe_to_agent":
                await self._handle_subscription_request(connection_id, data)
            elif message_type == "get_message_history":
                await self._handle_history_request(connection_id, data)
            elif message_type == "get_agent_status":
                await self._handle_status_request(connection_id, data)
            elif message_type == "ping":
                await self._send_to_connection(
                    connection_id,
                    {"type": "pong", "timestamp": datetime.now().isoformat()},
                )
            else:
                await self._send_to_connection(
                    connection_id,
                    {
                        "type": "error",
                        "message": f"Unknown message type: {message_type}",
                    },
                )
        except Exception as e:
            logger.error(f"Error handling client message: {e}")
            await self._send_to_connection(
                connection_id, {"type": "error", "message": "Internal server error"}
            )

    async def _handle_send_message_request(
        self, connection_id: str, sender_id: Optional[str], data: Dict[str, Any]
    ) -> None:
        """Handle request to send agent message."""
        if not sender_id:
            await self._send_to_connection(
                connection_id,
                {"type": "error", "message": "Agent ID required to send messages"},
            )
            return

        try:
            message_data = data.get("message", {})

            message_id = await self.message_bus.send_message(
                sender_id=sender_id,
                recipient_id=message_data.get("recipient_id"),
                message_type=MessageType(
                    message_data.get("message_type", "direct_message")
                ),
                subject=message_data.get("subject", "WebSocket Message"),
                content=message_data.get("content", {}),
                priority=MessagePriority(
                    message_data.get("priority", MessagePriority.NORMAL.value)
                ),
                expires_in_seconds=message_data.get("expires_in_seconds"),
                requires_acknowledgment=message_data.get(
                    "requires_acknowledgment", False
                ),
            )

            await self._send_to_connection(
                connection_id,
                {
                    "type": "message_sent",
                    "message_id": message_id,
                    "success": message_id is not None,
                },
            )
        except Exception as e:
            logger.error(f"Error sending agent message: {e}")
            await self._send_to_connection(
                connection_id,
                {"type": "error", "message": f"Failed to send message: {str(e)}"},
            )

    async def _handle_subscription_request(
        self, connection_id: str, data: Dict[str, Any]
    ) -> None:
        """Handle subscription to agent messages."""
        try:
            agent_id = data.get("agent_id")
            if not agent_id:
                await self._send_to_connection(
                    connection_id,
                    {"type": "error", "message": "Agent ID required for subscription"},
                )
                return

            # Update connection metadata
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["agent_id"] = agent_id

                # Update agent connections
                if agent_id not in self.agent_connections:
                    self.agent_connections[agent_id] = set()
                self.agent_connections[agent_id].add(connection_id)

            await self._send_to_connection(
                connection_id, {"type": "subscription_confirmed", "agent_id": agent_id}
            )
        except Exception as e:
            logger.error(f"Error handling subscription: {e}")
            await self._send_to_connection(
                connection_id, {"type": "error", "message": "Failed to subscribe"}
            )

    async def _handle_history_request(
        self, connection_id: str, data: Dict[str, Any]
    ) -> None:
        """Handle request for message history."""
        try:
            limit = data.get("limit", 50)
            agent_id = data.get("agent_id")
            message_type = data.get("message_type")

            # Convert message_type string to enum if provided
            if message_type:
                try:
                    message_type = MessageType(message_type)
                except ValueError:
                    message_type = None

            messages = self.message_bus.get_message_history(
                limit=limit, agent_id=agent_id, message_type=message_type
            )

            await self._send_to_connection(
                connection_id,
                {
                    "type": "message_history",
                    "messages": [msg.to_dict() for msg in messages],
                    "count": len(messages),
                },
            )
        except Exception as e:
            logger.error(f"Error getting message history: {e}")
            await self._send_to_connection(
                connection_id,
                {"type": "error", "message": "Failed to get message history"},
            )

    async def _handle_status_request(
        self, connection_id: str, data: Dict[str, Any]
    ) -> None:
        """Handle request for agent status."""
        try:
            # Get system stats
            system_stats = self.message_bus.get_system_stats()
            delivery_stats = self.message_bus.get_delivery_stats()
            subscription_stats = self.message_bus.get_subscription_stats()

            # Get connection stats
            connection_stats = {
                "total_connections": len(self.active_connections),
                "agent_connections": {
                    agent_id: len(conn_ids)
                    for agent_id, conn_ids in self.agent_connections.items()
                },
                "connection_details": dict(self.connection_metadata),
            }

            await self._send_to_connection(
                connection_id,
                {
                    "type": "agent_status",
                    "system_stats": system_stats,
                    "delivery_stats": delivery_stats,
                    "subscription_stats": subscription_stats,
                    "connection_stats": connection_stats,
                    "timestamp": datetime.now().isoformat(),
                },
            )
        except Exception as e:
            logger.error(f"Error getting agent status: {e}")
            await self._send_to_connection(
                connection_id,
                {"type": "error", "message": "Failed to get agent status"},
            )

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            "total_connections": len(self.active_connections),
            "agent_connections": {
                agent_id: len(conn_ids)
                for agent_id, conn_ids in self.agent_connections.items()
            },
            "connection_metadata": dict(self.connection_metadata),
        }


# Global instance to be used across the application
websocket_manager: Optional[WebSocketConnectionManager] = None


def initialize_websocket_manager(
    message_bus: AgentMessageBus,
) -> WebSocketConnectionManager:
    """Initialize the global WebSocket manager."""
    global websocket_manager
    websocket_manager = WebSocketConnectionManager(message_bus)
    return websocket_manager


def get_websocket_manager() -> Optional[WebSocketConnectionManager]:
    """Get the global WebSocket manager."""
    return websocket_manager


# WebSocket endpoint handlers for FastAPI


async def websocket_endpoint(
    websocket: WebSocket, connection_id: Optional[str] = None, agent_id: Optional[str] = None
):
    """Main WebSocket endpoint handler."""
    if not websocket_manager:
        await websocket.close(code=1008, reason="WebSocket manager not initialized")
        return

    if not connection_id:
        connection_id = str(uuid.uuid4())

    try:
        await websocket_manager.connect(websocket, connection_id, agent_id)

        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message_data = json.loads(data)

                # Handle client message
                await websocket_manager.handle_client_message(
                    connection_id, message_data
                )

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket_manager._send_to_connection(
                    connection_id, {"type": "error", "message": "Invalid JSON format"}
                )
            except Exception as e:
                logger.error(f"Error in WebSocket loop: {e}")
                await websocket_manager._send_to_connection(
                    connection_id, {"type": "error", "message": "Internal server error"}
                )

    except Exception as e:
        logger.error(f"Error in WebSocket endpoint: {e}")

    finally:
        await websocket_manager.disconnect(connection_id)


async def agent_collaboration_websocket(websocket: WebSocket, agent_id: str):
    """WebSocket endpoint specifically for agent collaboration."""
    connection_id = f"agent_{agent_id}_{int(time.time())}"
    await websocket_endpoint(websocket, connection_id, agent_id)


async def ide_websocket(websocket: WebSocket, user_id: str = "anonymous"):
    """WebSocket endpoint for IDE integration."""
    connection_id = f"ide_{user_id}_{int(time.time())}"
    await websocket_endpoint(websocket, connection_id, None)  # type: ignore[arg-type]
