# monitoring/health_check_system.py
"""
Unified Health Check System
Provides standardized health checking for all site containers with consistent endpoints,
monitoring, and alerting capabilities.
"""

import asyncio
import json
import logging
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiohttp
import docker
from docker import errors as docker_errors

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Health check status enumeration"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    STARTING = "starting"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Health check result data structure"""
    container_name: str
    container_id: str
    status: HealthStatus
    response_time_ms: float
    timestamp: datetime
    endpoint: str
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    details: Dict[str, Any] = None

    def __post_init__(self):
        if self.details is None:
            self.details = {}


@dataclass
class HealthCheckConfig:
    """Health check configuration for a container"""
    container_name: str
    endpoint: str = "/"
    port: int = 80
    timeout_seconds: int = 10
    interval_seconds: int = 30
    retries: int = 3
    start_period_seconds: int = 40
    expected_status_codes: List[int] = None

    def __post_init__(self):
        if self.expected_status_codes is None:
            self.expected_status_codes = [200, 301, 302]


class HealthCheckSystem:
    """
    Unified health check system for monitoring container health.
    Provides standardized health checking with configurable endpoints and monitoring.
    """

    def __init__(self, check_interval: int = 30):
        """Initialize health check system"""
        self.check_interval = check_interval
        self.docker_client = docker.from_env()
        self.health_configs: Dict[str, HealthCheckConfig] = {}
        self.health_history: Dict[str, List[HealthCheckResult]] = {}
        self.is_monitoring = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.max_history_size = 1000
        self.health_file = Path("data/health_checks.json")
        self.health_file.parent.mkdir(exist_ok=True)
        
        logger.info(f"Initialized HealthCheckSystem with {check_interval}s interval")

    async def start_monitoring(self):
        """Start continuous health monitoring"""
        if self.is_monitoring:
            logger.warning("Health monitoring already running")
            return
        
        # Auto-discover site containers and configure health checks
        await self._auto_configure_containers()
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Started health check monitoring")

    async def stop_monitoring(self):
        """Stop health monitoring"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Stopped health check monitoring")

    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self.check_all_containers()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(self.check_interval)

    async def _auto_configure_containers(self):
        """Auto-discover and configure health checks for site containers"""
        try:
            containers = self.docker_client.containers.list(
                filters={"name": "site-"}
            )
            
            for container in containers:
                if container.name not in self.health_configs:
                    # Get port mapping
                    port = self._get_container_port(container)
                    
                    # Create default health check config
                    config = HealthCheckConfig(
                        container_name=container.name,
                        endpoint="/",
                        port=port,
                        timeout_seconds=10,
                        interval_seconds=self.check_interval
                    )
                    
                    self.health_configs[container.name] = config
                    logger.info(f"Auto-configured health check for {container.name} on port {port}")
                    
        except Exception as e:
            logger.error(f"Error auto-configuring containers: {e}")

    def _get_container_port(self, container) -> int:
        """Get the external port for a container"""
        try:
            container.reload()
            ports = container.attrs.get('NetworkSettings', {}).get('Ports', {})
            
            # Look for port 80 mapping first
            if '80/tcp' in ports and ports['80/tcp']:
                return int(ports['80/tcp'][0]['HostPort'])
            
            # Fall back to any mapped port
            for port_spec, mappings in ports.items():
                if mappings:
                    return int(mappings[0]['HostPort'])
            
            return 8080  # Default fallback
            
        except (KeyError, IndexError, ValueError, TypeError):
            return 8080

    async def check_all_containers(self) -> Dict[str, HealthCheckResult]:
        """Perform health checks on all configured containers"""
        results = {}
        
        # Update container configurations
        await self._auto_configure_containers()
        
        # Perform health checks
        for container_name, config in self.health_configs.items():
            try:
                result = await self._perform_health_check(config)
                results[container_name] = result
                
                # Add to history
                if container_name not in self.health_history:
                    self.health_history[container_name] = []
                
                self.health_history[container_name].append(result)
                
                # Maintain history size
                if len(self.health_history[container_name]) > self.max_history_size:
                    self.health_history[container_name] = self.health_history[container_name][-self.max_history_size:]
                
            except Exception as e:
                logger.error(f"Error checking health for {container_name}: {e}")
                
                # Create error result
                error_result = HealthCheckResult(
                    container_name=container_name,
                    container_id="unknown",
                    status=HealthStatus.UNKNOWN,
                    response_time_ms=0.0,
                    timestamp=datetime.now(),
                    endpoint=config.endpoint,
                    error_message=str(e)
                )
                results[container_name] = error_result
        
        # Save results to file
        await self._save_health_results()
        
        logger.debug(f"Completed health checks for {len(results)} containers")
        return results

    async def _perform_health_check(self, config: HealthCheckConfig) -> HealthCheckResult:
        """Perform health check for a single container"""
        start_time = time.time()
        
        try:
            # Get container info
            container = self.docker_client.containers.get(config.container_name)
            container_id = container.id[:12]
            
            # Check if container is running
            if container.status != 'running':
                return HealthCheckResult(
                    container_name=config.container_name,
                    container_id=container_id,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=0.0,
                    timestamp=datetime.now(),
                    endpoint=config.endpoint,
                    error_message=f"Container not running (status: {container.status})"
                )
            
            # Perform HTTP health check
            url = f"http://localhost:{config.port}{config.endpoint}"
            
            timeout = aiohttp.ClientTimeout(total=config.timeout_seconds)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    # Determine health status
                    if response.status in config.expected_status_codes:
                        status = HealthStatus.HEALTHY
                        error_message = None
                    else:
                        status = HealthStatus.UNHEALTHY
                        error_message = f"Unexpected status code: {response.status}"
                    
                    # Get response details
                    try:
                        response_text = await response.text()
                        details = {
                            "response_size": len(response_text),
                            "content_type": response.headers.get('content-type', 'unknown')
                        }
                    except Exception:
                        details = {}
                    
                    return HealthCheckResult(
                        container_name=config.container_name,
                        container_id=container_id,
                        status=status,
                        response_time_ms=round(response_time, 2),
                        timestamp=datetime.now(),
                        endpoint=config.endpoint,
                        status_code=response.status,
                        error_message=error_message,
                        details=details
                    )
                    
        except asyncio.TimeoutError:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                container_name=config.container_name,
                container_id=container_id if 'container_id' in locals() else "unknown",
                status=HealthStatus.UNHEALTHY,
                response_time_ms=round(response_time, 2),
                timestamp=datetime.now(),
                endpoint=config.endpoint,
                error_message=f"Health check timeout after {config.timeout_seconds}s"
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                container_name=config.container_name,
                container_id=container_id if 'container_id' in locals() else "unknown",
                status=HealthStatus.UNHEALTHY,
                response_time_ms=round(response_time, 2),
                timestamp=datetime.now(),
                endpoint=config.endpoint,
                error_message=str(e)
            )

    async def _save_health_results(self):
        """Save health check results to file"""
        try:
            data = {
                'timestamp': datetime.now().isoformat(),
                'containers': {}
            }
            
            for container_name, history in self.health_history.items():
                # Save last 50 results for file storage
                recent_results = history[-50:] if history else []
                data['containers'][container_name] = [
                    {
                        **asdict(result),
                        'timestamp': result.timestamp.isoformat(),
                        'status': result.status.value
                    }
                    for result in recent_results
                ]
            
            with open(self.health_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving health results: {e}")

    def configure_health_check(self, container_name: str, config: HealthCheckConfig):
        """Configure health check for a specific container"""
        self.health_configs[container_name] = config
        logger.info(f"Configured health check for {container_name}")

    def get_container_health(self, container_name: str) -> Optional[HealthCheckResult]:
        """Get latest health check result for a container"""
        history = self.health_history.get(container_name, [])
        return history[-1] if history else None

    def get_health_summary(self, container_name: str, minutes: int = 60) -> Dict[str, Any]:
        """Get health summary for a container"""
        history = self.health_history.get(container_name, [])
        if not history:
            return {"error": f"No health data found for {container_name}"}
        
        # Filter recent results
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_results = [r for r in history if r.timestamp >= cutoff_time]
        
        if not recent_results:
            return {"error": f"No recent health data found for {container_name}"}
        
        latest = recent_results[-1]
        healthy_count = sum(1 for r in recent_results if r.status == HealthStatus.HEALTHY)
        
        return {
            "container_name": container_name,
            "current_status": latest.status.value,
            "current_response_time_ms": latest.response_time_ms,
            "last_check": latest.timestamp.isoformat(),
            "health_percentage": (healthy_count / len(recent_results)) * 100,
            "total_checks": len(recent_results),
            "healthy_checks": healthy_count,
            "average_response_time_ms": sum(r.response_time_ms for r in recent_results) / len(recent_results),
            "max_response_time_ms": max(r.response_time_ms for r in recent_results),
            "min_response_time_ms": min(r.response_time_ms for r in recent_results),
            "period_minutes": minutes
        }

    def get_all_health_status(self) -> Dict[str, Dict[str, Any]]:
        """Get current health status for all containers"""
        status = {}
        for container_name in self.health_configs.keys():
            latest = self.get_container_health(container_name)
            if latest:
                status[container_name] = {
                    "status": latest.status.value,
                    "response_time_ms": latest.response_time_ms,
                    "last_check": latest.timestamp.isoformat(),
                    "endpoint": latest.endpoint,
                    "error_message": latest.error_message
                }
        return status

    async def cleanup_old_health_data(self, days: int = 7):
        """Clean up health data older than specified days"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        for container_name, history in self.health_history.items():
            original_count = len(history)
            self.health_history[container_name] = [
                r for r in history if r.timestamp >= cutoff_time
            ]
            cleaned_count = original_count - len(self.health_history[container_name])
            
            if cleaned_count > 0:
                logger.info(f"Cleaned {cleaned_count} old health records for {container_name}")
        
        logger.info(f"Completed health data cleanup for data older than {days} days")
