# Trend Monitoring Service - Standalone Docker Compose
# This is a specialized compose file for running only the trend monitoring service
# For full application stack, use containers/docker-compose.yml

networks:
  ai-coding-network:
    driver: bridge
    name: ai-coding-network
services:
  trend-monitor:
    build:
      context: ..
      dockerfile: trend_monitoring/Dockerfile
    container_name: ai-coding-trend-monitor
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    env_file: ../.env
    environment:
      PYTHONPATH: /app
      PYTHONUNBUFFERED: '1'
      TREND_ANALYSIS_WINDOW: '30'
      TREND_DATA_DIR: /app/data/trends
      TREND_MONITORING_ENABLED: 'true'
      TREND_UPDATE_INTERVAL: '3600'
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8080/health
      timeout: 10s
    labels:
    - ai-coding.service=trend-monitor
    - ai-coding.version=1.0.0
    - ai-coding.description=Trend Monitoring Service
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ../data/trends:/app/data/trends
    - ../data/ai_optimization:/app/data/ai_optimization
    - ../logs:/app/logs
    - ../config:/app/config:ro
    - ../backups:/app/backups
    - ../test_reports:/app/test_reports
    - ../uploads:/app/uploads
    - containers\extracted\docker-compose_trend-monitor_ports.json:/app/config/ports.json:ro
  trend-monitor-redis:
    container_name: ai-coding-trend-monitor-redis
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 10s
      test:
      - CMD
      - redis-cli
      - ping
      timeout: 10s
    image: redis:7-alpine
    labels:
    - ai-coding.service=trend-monitor-redis
    - ai-coding.version=7-alpine
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - trend_monitor_redis_data:/data
    - containers\extracted\docker-compose_trend-monitor-redis_ports.json:/app/config/ports.json:ro
version: '3.8'
volumes:
  trend_monitor_redis_data:
    driver: local
