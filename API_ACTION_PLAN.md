# 🚀 API Integration Action Plan

**Priority:** CRITICAL
**Timeline:** Immediate to 2 weeks
**Goal:** Integrate Phase 19 features with API endpoints

## 🔥 **IMMEDIATE ACTIONS (Today)**

### **1. Fix Critical API Signature Issue**
**File:** `api/site_container_routes.py`
**Time:** 15 minutes

```bash
# Fix the create_site_container call
# Line 51: Change from positional to keyword arguments
```

**Code Change:**
```python
# BEFORE (BROKEN):
result = await container_manager.create_site_container(
    request.site_name, site_config
)

# AFTER (FIXED):
result = await container_manager.create_site_container(
    site_name=request.site_name,
    site_config=site_config
)
```

### **2. Test Current API Endpoints**
**Time:** 30 minutes

```bash
# Test all existing endpoints to ensure they work
python -m pytest tests/test_api/ -v
# OR manually test with curl/Postman
```

## 📅 **DAY 1-2: Core Integration**

### **3. Create AI Container Management Routes**
**File:** `api/ai_container_routes.py` (NEW)
**Time:** 2 hours

**Required Endpoints:**
```python
POST /api/ai-containers/analyze/{site_name}
POST /api/ai-containers/optimize/{site_name}
GET  /api/ai-containers/confidence/{site_name}
POST /api/ai-containers/generate-dockerfile/{site_name}
```

### **4. Create Monitoring Dashboard Routes**
**File:** `api/monitoring_dashboard_routes.py` (NEW)
**Time:** 2 hours

**Required Endpoints:**
```python
GET  /api/monitoring/dashboard/data
GET  /api/monitoring/containers/{container_name}/metrics
GET  /api/monitoring/health-checks
GET  /api/monitoring/system-status
```

### **5. Create Port Management Routes**
**File:** `api/port_management_routes.py` (NEW)
**Time:** 1 hour

**Required Endpoints:**
```python
POST /api/ports/allocate/{site_name}
POST /api/ports/release/{site_name}
GET  /api/ports/list
GET  /api/ports/available
```

## 📅 **DAY 3-5: Enhanced Features**

### **6. Create Health Check Routes**
**File:** `api/health_check_routes.py` (NEW)
**Time:** 1.5 hours

**Required Endpoints:**
```python
GET  /api/health/containers
GET  /api/health/containers/{container_name}
POST /api/health/configure/{container_name}
GET  /api/health/summary
```

### **7. Create Alert Management Routes**
**File:** `api/alert_management_routes.py` (NEW)
**Time:** 1.5 hours

**Required Endpoints:**
```python
GET  /api/alerts
GET  /api/alerts/{alert_id}
POST /api/alerts/{alert_id}/acknowledge
POST /api/alerts/configure
GET  /api/alerts/summary
```

### **8. Update Main API Registration**
**File:** `api/main.py`
**Time:** 30 minutes

```python
# Add new route imports and registrations
from api import ai_container_routes
from api import monitoring_dashboard_routes
from api import port_management_routes
from api import health_check_routes
from api import alert_management_routes

app.include_router(ai_container_routes.router)
app.include_router(monitoring_dashboard_routes.router)
app.include_router(port_management_routes.router)
app.include_router(health_check_routes.router)
app.include_router(alert_management_routes.router)
```

## 📅 **WEEK 2: Polish & Testing**

### **9. Standardize Response Models**
**File:** `api/models.py`
**Time:** 2 hours

```python
class StandardResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class ContainerResponse(StandardResponse):
    container_name: Optional[str] = None
    port: Optional[int] = None
    status: Optional[str] = None

class MonitoringResponse(StandardResponse):
    metrics: Optional[Dict[str, Any]] = None
    alerts: Optional[List[Dict[str, Any]]] = None
```

### **10. Update Configuration**
**File:** `api/config.py`
**Time:** 1 hour

```python
# Add Phase 19 configuration
monitoring_enabled: bool = True
monitoring_port: int = 8090
port_range_start: int = 8080
port_range_end: int = 9000
ai_optimization_enabled: bool = True
health_check_interval: int = 30
alert_email_enabled: bool = False
```

### **11. Add Error Handling Middleware**
**File:** `api/middleware.py`
**Time:** 1 hour

```python
@app.middleware("http")
async def error_handling_middleware(request: Request, call_next):
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        logger.error(f"API Error: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )
```

### **12. Create API Tests**
**File:** `tests/test_api/` (NEW DIRECTORY)
**Time:** 4 hours

**Test Files Needed:**
- `test_ai_container_routes.py`
- `test_monitoring_routes.py`
- `test_port_management_routes.py`
- `test_health_check_routes.py`
- `test_alert_management_routes.py`

## 🧪 **Testing Checklist**

### **Manual Testing:**
- [x] All existing endpoints still work
- [x] New AI container endpoints work
- [x] Monitoring dashboard endpoints return data
- [x] Port management endpoints work
- [x] Health check endpoints work
- [x] Alert management endpoints work

### **Automated Testing:**
- [ ] Unit tests for all new routes
- [ ] Integration tests with Phase 19 components
- [ ] Error handling tests
- [ ] Performance tests for monitoring endpoints

## 📊 **Success Criteria**

### **Functional Requirements:**
- ✅ All Phase 19 features accessible via API
- ✅ Consistent response formats across all endpoints
- ✅ Proper error handling and logging
- ✅ API documentation updated

### **Quality Requirements:**
- ✅ 100% test coverage for new endpoints
- ✅ Response times < 500ms for most endpoints
- ✅ Proper input validation
- ✅ Security best practices implemented

## 🔧 **Quick Commands**

### **Start API Server:**
```bash
# Activate virtual environment
.\.venv\Scripts\Activate.ps1

# Start API server
cd api
python main.py
```

### **Test API Endpoints:**
```bash
# Test health endpoint
curl http://localhost:8000/health

# Test container creation
curl -X POST http://localhost:8000/api/site-containers/create \
  -H "Content-Type: application/json" \
  -d '{"site_name": "test-site", "environment": "development"}'
```

### **Run API Tests:**
```bash
# Run all API tests
python -m pytest tests/test_api/ -v

# Run specific test file
python -m pytest tests/test_api/test_site_container_routes.py -v
```

## 📞 **Support Resources**

### **Documentation:**
- FastAPI docs: https://fastapi.tiangolo.com/
- Pydantic models: https://pydantic-docs.helpmanual.io/
- API testing: https://fastapi.tiangolo.com/tutorial/testing/

### **Code Examples:**
- Check `api/enhanced_site_container_routes.py` for patterns
- Review `core/` modules for integration examples
- See `tests/test_container_management.py` for testing patterns

---

**Remember:** Always activate the virtual environment before working with API code, and test each endpoint after implementation!
