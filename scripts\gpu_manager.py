import asyncio
import json
import logging
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Tuple

import GPUtil
import psutil

#!/usr/bin/env python3
"""
GPU Manager for AI Coding Agent

Manages GPU allocation and scheduling for NVIDIA Quadro P1000 (4GB).
Provides priority-based allocation, resource monitoring, and optimization
for AI services including Ollama, fine-tuning, and model optimization.

Features:
- Priority-based GPU allocation
- Memory and utilization monitoring
- Temperature and power tracking
- Request queuing and scheduling
- Optimization recommendations
"""


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ServicePriority(Enum):
    """
    Service priority levels for GPU allocation.

    Lower numeric values indicate higher priority.
    OLLAMA has highest priority for real-time inference.
    """

    OLLAMA = 1
    FINE_TUNER = 2
    MODEL_OPTIMIZER = 3


@dataclass
class GPURequest:
    """
    GPU allocation request containing service details and requirements.

    Attributes:
        service_name: Name of the requesting service
        priority: Service priority level
        memory_required: Required GPU memory in MB
        duration_estimate: Estimated usage duration in seconds
        request_time: Timestamp when request was made
        request_id: Unique identifier for the request
    """

    service_name: str
    priority: ServicePriority
    memory_required: int  # in MB
    duration_estimate: int  # in seconds
    request_time: float
    request_id: str


@dataclass
class GPUStatus:
    """
    Current GPU status and metrics.

    Attributes:
        total_memory: Total GPU memory in MB
        used_memory: Currently used memory in MB
        available_memory: Available memory in MB
        utilization: GPU utilization percentage (0-100)
        temperature: GPU temperature in Celsius
        power_usage: Power consumption in watts
        active_services: List of currently active service names
    """

    total_memory: int  # in MB
    used_memory: int  # in MB
    available_memory: int  # in MB
    utilization: float  # percentage
    temperature: float  # celsius
    power_usage: float  # watts
    active_services: List[str]


class GPUManager:
    """
    Manages GPU allocation for AI Coding Agent services.

    Provides centralized GPU resource management with priority-based allocation,
    monitoring, and optimization for NVIDIA Quadro P1000 (4GB).

    Features:
    - Priority-based allocation (Ollama > Fine-tuner > Model Optimizer)
    - Memory and utilization monitoring
    - Request queuing and scheduling
    - Temperature and power tracking
    - Optimization recommendations
    """

    def __init__(self, config_path: str = "config/gpu_optimization_config.json"):
        """
        Initialize GPU Manager.

        Args:
            config_path: Path to GPU configuration file

        Sets up GPU status tracking, request queues, and loads configuration.
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.gpu_requests: Dict[str, GPURequest] = {}
        self.active_allocations: Dict[str, GPURequest] = {}
        self.gpu_status = GPUStatus(
            total_memory=4096,  # 4GB Quadro P1000
            used_memory=0,
            available_memory=4096,
            utilization=0.0,
            temperature=0.0,
            power_usage=0.0,
            active_services=[],
        )
        self.lock = asyncio.Lock()

    def _load_config(self) -> Dict:
        """
        Load GPU configuration from file.

        Returns:
            dict: Configuration dictionary, empty dict if file not found
        """
        try:
            with open(self.config_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(
                f"GPU config not found at {self.config_path}, using defaults"
            )
            return {}

    async def get_gpu_status(self) -> GPUStatus:
        """
        Get current GPU status and metrics.

        Updates internal GPU status with current memory usage, utilization,
        temperature, and power consumption from the GPU.

        Returns:
            GPUStatus: Current GPU status and metrics
        """
        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]  # Quadro P1000
                self.gpu_status.used_memory = int(gpu.memoryUsed)
                self.gpu_status.available_memory = int(gpu.memoryFree)
                self.gpu_status.utilization = float(gpu.load * 100)
                self.gpu_status.temperature = float(gpu.temperature)
                self.gpu_status.power_usage = (
                    float(gpu.power) if hasattr(gpu, "power") else 0.0
                )
                self.gpu_status.active_services = list(
                    self.active_allocations.keys())
        except Exception as e:
            logger.error(f"Error getting GPU status: {e}")

        return self.gpu_status

    async def request_gpu_allocation(
        self,
        service_name: str,
        priority: ServicePriority,
        memory_required: int,
        duration_estimate: int = 3600,
    ) -> Tuple[bool, str]:
        """
        Request GPU allocation for a service.

        Args:
            service_name: Name of the requesting service
            priority: Service priority level
            memory_required: Required GPU memory in MB
            duration_estimate: Estimated usage duration in seconds

        Returns:
            tuple: (success: bool, request_id: str)
                success: True if allocated immediately, False if queued
                request_id: Unique identifier for the request
        """
        async with self.lock:
            request_id = f"{service_name}_{int(time.time())}"

            request = GPURequest(
                service_name=service_name,
                priority=priority,
                memory_required=memory_required,
                duration_estimate=duration_estimate,
                request_time=time.time(),
                request_id=request_id,
            )

            # Check if we can allocate GPU
            can_allocate, reason = await self._can_allocate_gpu(request)

            if can_allocate:
                self.active_allocations[service_name] = request
                logger.info(
                    f"GPU allocated to {service_name} ({memory_required}MB)")
                return True, request_id
            else:
                # Add to waiting queue
                self.gpu_requests[request_id] = request
                logger.info(f"GPU request queued for {service_name}: {reason}")
                return False, request_id

    async def release_gpu_allocation(self, service_name: str) -> bool:
        """
        Release GPU allocation for a service.

        Args:
            service_name: Name of the service to release GPU from

        Returns:
            bool: True if successfully released, False if service not found
        """
        async with self.lock:
            if service_name in self.active_allocations:
                del self.active_allocations[service_name]
                logger.info(f"GPU released from {service_name}")

                # Process waiting queue
                await self._process_waiting_queue()
                return True
            return False

    async def _can_allocate_gpu(self, request: GPURequest) -> Tuple[bool, str]:
        """
        Check if GPU can be allocated for the request.

        Validates memory availability, priority conflicts, utilization,
        and temperature before allowing allocation.

        Args:
            request: GPU allocation request to validate

        Returns:
            tuple: (can_allocate: bool, reason: str)
        """
        # Get current GPU status
        await self.get_gpu_status()

        # Check memory availability
        if request.memory_required > self.gpu_status.available_memory:
            return (
                False,
                f"Insufficient GPU memory. Required: {request.memory_required}MB, Available: {self.gpu_status.available_memory}MB",
            )

        # Check if higher priority service is running
        for active_service, active_request in self.active_allocations.items():
            if active_request.priority.value < request.priority.value:
                return False, f"Higher priority service {active_service} is using GPU"

        # Check GPU utilization
        if self.gpu_status.utilization > 90:
            return False, f"GPU utilization too high: {self.gpu_status.utilization}%"

        # Check temperature
        if self.gpu_status.temperature > 85:
            return False, f"GPU temperature too high: {self.gpu_status.temperature}°C"

        return True, "GPU allocation possible"

    async def _process_waiting_queue(self):
        """
        Process waiting GPU requests in priority order.

        Sorts pending requests by priority and request time,
        then attempts to allocate GPU to the highest priority request.
        """
        if not self.gpu_requests:
            return

        # Sort requests by priority and time
        sorted_requests = sorted(
            self.gpu_requests.items(),
            key=lambda x: (x[1].priority.value, x[1].request_time),
        )

        for request_id, request in sorted_requests:
            can_allocate, reason = await self._can_allocate_gpu(request)
            if can_allocate:
                self.active_allocations[request.service_name] = request
                del self.gpu_requests[request_id]
                logger.info(
                    f"GPU allocated to queued request: {request.service_name}")
                break

    async def get_allocation_status(self) -> Dict:
        """
        Get current allocation status for all services.

        Returns:
            dict: Status containing active allocations, waiting requests, and GPU status
        """
        async with self.lock:
            return {
                "active_allocations": {
                    service: {
                        "priority": req.priority.name,
                        "memory_required": req.memory_required,
                        "duration_estimate": req.duration_estimate,
                        "request_time": req.request_time,
                    }
                    for service, req in self.active_allocations.items()
                },
                "waiting_requests": {
                    req_id: {
                        "service_name": req.service_name,
                        "priority": req.priority.name,
                        "memory_required": req.memory_required,
                        "request_time": req.request_time,
                    }
                    for req_id, req in self.gpu_requests.items()
                },
                "gpu_status": {
                    "total_memory": self.gpu_status.total_memory,
                    "used_memory": self.gpu_status.used_memory,
                    "available_memory": self.gpu_status.available_memory,
                    "utilization": self.gpu_status.utilization,
                    "temperature": self.gpu_status.temperature,
                    "power_usage": self.gpu_status.power_usage,
                },
            }

    async def optimize_gpu_usage(self) -> Dict:
        """
        Optimize GPU usage based on current allocations.

        Analyzes current allocations for idle services, memory fragmentation,
        and temperature issues, providing optimization recommendations.

        Returns:
            dict: Optimization analysis with detected issues and recommendations
        """
        async with self.lock:
            optimizations = []

            # Check for idle services
            current_time = time.time()
            for service_name, request in list(self.active_allocations.items()):
                if current_time - request.request_time > request.duration_estimate:
                    optimizations.append(f"Service {service_name} may be idle")

            # Check memory fragmentation
            if (
                self.gpu_status.used_memory > 0
                and self.gpu_status.available_memory < 512
            ):
                optimizations.append("GPU memory fragmentation detected")

            # Check temperature
            if self.gpu_status.temperature > 80:
                optimizations.append(
                    "GPU temperature high, consider reducing load")

            return {
                "optimizations": optimizations,
                "recommendations": self._get_optimization_recommendations(),
            }

    def _get_optimization_recommendations(self) -> List[str]:
        """
        Get optimization recommendations based on current GPU state.

        Returns:
            list: List of optimization recommendation strings
        """
        recommendations = []

        if self.gpu_status.utilization > 80:
            recommendations.append(
                "Consider reducing concurrent GPU operations")

        if self.gpu_status.temperature > 75:
            recommendations.append(
                "Monitor GPU temperature, ensure proper cooling")

        if len(self.active_allocations) > 1:
            recommendations.append(
                "Multiple services using GPU, consider scheduling")

        return recommendations

    async def emergency_gpu_release(self, service_name: str) -> bool:
        """
        Emergency release of GPU allocation for a service.

        Force-releases GPU allocation without normal cleanup procedures.
        Used in critical situations or when normal release fails.

        Args:
            service_name: Name of service to emergency release

        Returns:
            bool: True if successfully released, False if service not found
        """
        async with self.lock:
            if service_name in self.active_allocations:
                logger.warning(f"Emergency GPU release for {service_name}")
                del self.active_allocations[service_name]
                return True
            return False

    async def get_gpu_metrics(self) -> Dict:
        """
        Get detailed GPU metrics including memory, performance, and allocations.

        Returns:
            dict: Comprehensive GPU metrics or error information
        """
        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]
                return {
                    "memory": {
                        "total": int(gpu.memoryTotal),
                        "used": int(gpu.memoryUsed),
                        "free": int(gpu.memoryFree),
                        "utilization": float(gpu.memoryUtil * 100),
                    },
                    "performance": {
                        "utilization": float(gpu.load * 100),
                        "temperature": float(gpu.temperature),
                        "power": float(gpu.power) if hasattr(gpu, "power") else 0.0,
                    },
                    "allocations": await self.get_allocation_status(),
                }
        except Exception as e:
            logger.error(f"Error getting GPU metrics: {e}")
            return {"error": str(e)}


# Global GPU manager instance
gpu_manager = GPUManager()


async def main():
    """
    Test GPU manager functionality.

    Demonstrates GPU status checking, allocation requests,
    status monitoring, and optimization features.
    """
    print("🧪 Testing GPU Manager...")

    # Test GPU status
    status = await gpu_manager.get_gpu_status()
    print(f"GPU Status: {status}")

    # Test allocation request
    success, request_id = await gpu_manager.request_gpu_allocation(
        "ollama", ServicePriority.OLLAMA, 2048, 3600
    )
    print(f"Ollama GPU request: {success}, ID: {request_id}")

    # Test allocation status
    allocation_status = await gpu_manager.get_allocation_status()
    print(f"Allocation Status: {allocation_status}")

    # Test optimization
    optimization = await gpu_manager.optimize_gpu_usage()
    print(f"Optimization: {optimization}")


if __name__ == "__main__":
    asyncio.run(main())
