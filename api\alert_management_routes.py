# api/alert_management_routes.py
"""
Alert Management API Routes
Provides REST API endpoints for retrieving and managing container alerts.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from api.agent_dependency import get_agent
from monitoring.alerting_system import AlertingSystem, AlertSeverity, AlertType

router = APIRouter(prefix="/api/alerts", tags=["Alert Management"])


class AlertResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str
    error: Optional[str] = None


@router.get("/", response_model=AlertResponse)
async def list_alerts(
    severity: Optional[str] = Query(None, description="Filter by severity: low, medium, high, critical"),
    alert_type: Optional[str] = Query(None, description="Filter by alert type"),
    container_name: Optional[str] = Query(None, description="Filter by container name"),
    agent=Depends(get_agent)
):
    """List active alerts with optional filtering"""
    try:
        alerting = AlertingSystem()
        alerts = alerting.get_active_alerts()

        # Apply filters
        if severity:
            severity = severity.lower()
            alerts = [a for a in alerts if a.severity.value == severity]
        if alert_type:
            alert_type = alert_type.lower()
            alerts = [a for a in alerts if a.alert_type.value == alert_type]
        if container_name:
            alerts = [a for a in alerts if a.container_name == container_name]

        def _val(x, attr):
            try:
                v = getattr(x, attr)
                return getattr(v, "value", v)
            except Exception:
                return None
        def _iso(ts):
            try:
                return ts.isoformat()
            except Exception:
                return str(ts)
        data = {
            "alerts": [
                {
                    "id": getattr(a, "id", None),
                    "type": _val(a, "alert_type"),
                    "severity": _val(a, "severity"),
                    "container_name": getattr(a, "container_name", None),
                    "message": getattr(a, "message", None),
                    "timestamp": _iso(getattr(a, "timestamp", None)),
                    "resolved": getattr(a, "resolved", False),
                    "resolved_timestamp": _iso(getattr(a, "resolved_timestamp", None)) if getattr(a, "resolved_timestamp", None) else None,
                    "metadata": getattr(a, "metadata", getattr(a, "details", {})),
                }
                for a in alerts
            ],
            "summary": {
                "total": len(alerts),
                "by_severity": {
                    s: len([a for a in alerts if a.severity.value == s])
                    for s in ["low", "medium", "high", "critical"]
                },
                "by_type": {}
            },
        }

        return AlertResponse(
            success=True,
            message=f"Retrieved {len(alerts)} active alerts",
            data=data,
            timestamp=datetime.now().isoformat(),
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


class AcknowledgeRequest(BaseModel):
    acknowledge: bool = True


@router.post("/{alert_id}/acknowledge", response_model=AlertResponse)
async def acknowledge_alert(alert_id: str, request: AcknowledgeRequest, agent=Depends(get_agent)):
    """Acknowledge an alert (mark as acknowledged)"""
    try:
        alerting = AlertingSystem()
        success = alerting.acknowledge_alert(alert_id)

        if not success:
            raise HTTPException(status_code=404, detail=f"Alert {alert_id} not found")

        return AlertResponse(
            success=True,
            message=f"Alert {alert_id} acknowledged",
            data={"alert_id": alert_id, "acknowledged": request.acknowledge},
            timestamp=datetime.now().isoformat(),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{alert_id}", response_model=AlertResponse)
async def get_alert_details(alert_id: str, agent=Depends(get_agent)):
    """Get detailed information for a specific alert"""
    try:
        alerting = AlertingSystem()
        alert = alerting.get_alert_details(alert_id)

        if not alert:
            raise HTTPException(status_code=404, detail=f"Alert {alert_id} not found")

        def _val(x, attr):
            try:
                v = getattr(x, attr)
                return getattr(v, "value", v)
            except Exception:
                return None
        def _iso(ts):
            try:
                return ts.isoformat()
            except Exception:
                return str(ts)
        data = {
            "id": getattr(alert, "id", None),
            "type": _val(alert, "alert_type"),
            "severity": _val(alert, "severity"),
            "container_name": getattr(alert, "container_name", None),
            "message": getattr(alert, "message", None),
            "timestamp": _iso(getattr(alert, "timestamp", None)),
            "resolved": getattr(alert, "resolved", False),
            "resolved_timestamp": _iso(getattr(alert, "resolved_timestamp", None)) if getattr(alert, "resolved_timestamp", None) else None,
            "metadata": getattr(alert, "metadata", getattr(alert, "details", {})),
        }

        return AlertResponse(
            success=True,
            message=f"Retrieved details for alert {alert_id}",
            data=data,
            timestamp=datetime.now().isoformat(),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
