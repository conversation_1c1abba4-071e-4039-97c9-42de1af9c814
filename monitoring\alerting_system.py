# monitoring/alerting_system.py
"""
Container Alerting System
Monitors container health and performance metrics to generate alerts for failures,
resource exhaustion, and performance issues.
"""

import asyncio
import json
import logging
import smtplib
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from monitoring.container_metrics_collector import ContainerMetricsCollector, ContainerMetrics
from monitoring.health_check_system import HealthCheckSystem, HealthStatus

logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertType(Enum):
    """Types of alerts"""
    CONTAINER_DOWN = "container_down"
    HEALTH_CHECK_FAILED = "health_check_failed"
    HIGH_CPU_USAGE = "high_cpu_usage"
    HIGH_MEMORY_USAGE = "high_memory_usage"
    CONTAINER_RESTART = "container_restart"
    DISK_SPACE_LOW = "disk_space_low"
    NETWORK_ISSUES = "network_issues"
    PERFORMANCE_DEGRADATION = "performance_degradation"


@dataclass
class Alert:
    """Alert data structure"""
    id: str
    alert_type: AlertType
    severity: AlertSeverity
    container_name: str
    message: str
    timestamp: datetime
    resolved: bool = False
    resolved_timestamp: Optional[datetime] = None
    metadata: Dict[str, Any] = None
    acknowledged: bool = False

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    alert_type: AlertType
    severity: AlertSeverity
    condition: str  # Python expression to evaluate
    threshold_value: float
    duration_minutes: int = 5  # How long condition must persist
    cooldown_minutes: int = 30  # Minimum time between alerts
    enabled: bool = True
    containers: List[str] = None  # Specific containers, None for all

    def __post_init__(self):
        if self.containers is None:
            self.containers = []


class AlertingSystem:
    """
    Container alerting system for monitoring and notifications.
    Monitors metrics and health status to generate alerts based on configurable rules.
    """

    def __init__(self, check_interval: int = 60):
        """Initialize alerting system"""
        self.check_interval = check_interval
        self.metrics_collector = ContainerMetricsCollector()
        self.health_system = HealthCheckSystem()
        self.alert_rules: List[AlertRule] = []
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.last_alert_times: Dict[str, datetime] = {}
        self.is_monitoring = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.alerts_file = Path("data/alerts.json")
        self.alerts_file.parent.mkdir(exist_ok=True)

        # Email configuration
        self.email_config = self._load_email_config()

        # Initialize default alert rules
        self._setup_default_rules()

        logger.info(f"Initialized AlertingSystem with {check_interval}s interval")

    def _load_email_config(self) -> Dict[str, Any]:
        """Load email configuration from file"""
        config_file = Path("config/email_config.json")
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading email config: {e}")

        return {
            "enabled": False,
            "smtp_server": "localhost",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "from_email": "<EMAIL>",
            "to_emails": []
        }

    def _setup_default_rules(self):
        """Setup default alert rules"""
        default_rules = [
            AlertRule(
                name="High CPU Usage",
                alert_type=AlertType.HIGH_CPU_USAGE,
                severity=AlertSeverity.MEDIUM,
                condition="cpu_usage_percent > threshold_value",
                threshold_value=80.0,
                duration_minutes=5,
                cooldown_minutes=15
            ),
            AlertRule(
                name="Critical CPU Usage",
                alert_type=AlertType.HIGH_CPU_USAGE,
                severity=AlertSeverity.HIGH,
                condition="cpu_usage_percent > threshold_value",
                threshold_value=95.0,
                duration_minutes=2,
                cooldown_minutes=10
            ),
            AlertRule(
                name="High Memory Usage",
                alert_type=AlertType.HIGH_MEMORY_USAGE,
                severity=AlertSeverity.MEDIUM,
                condition="memory_usage_percent > threshold_value",
                threshold_value=85.0,
                duration_minutes=5,
                cooldown_minutes=15
            ),
            AlertRule(
                name="Critical Memory Usage",
                alert_type=AlertType.HIGH_MEMORY_USAGE,
                severity=AlertSeverity.CRITICAL,
                condition="memory_usage_percent > threshold_value",
                threshold_value=95.0,
                duration_minutes=2,
                cooldown_minutes=5
            ),
            AlertRule(
                name="Container Health Check Failed",
                alert_type=AlertType.HEALTH_CHECK_FAILED,
                severity=AlertSeverity.HIGH,
                condition="health_status == 'unhealthy'",
                threshold_value=1.0,
                duration_minutes=2,
                cooldown_minutes=10
            ),
            AlertRule(
                name="Container Restart",
                alert_type=AlertType.CONTAINER_RESTART,
                severity=AlertSeverity.MEDIUM,
                condition="restart_count > threshold_value",
                threshold_value=0.0,
                duration_minutes=1,
                cooldown_minutes=30
            )
        ]

        self.alert_rules.extend(default_rules)
        logger.info(f"Setup {len(default_rules)} default alert rules")

    async def start_monitoring(self):
        """Start alert monitoring"""
        if self.is_monitoring:
            logger.warning("Alert monitoring already running")
            return

        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Started alert monitoring")

    async def stop_monitoring(self):
        """Stop alert monitoring"""
        if not self.is_monitoring:
            return

        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        logger.info("Stopped alert monitoring")

    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._check_alerts()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert monitoring loop: {e}")
                await asyncio.sleep(self.check_interval)

    async def _check_alerts(self):
        """Check all alert rules against current metrics"""
        try:
            # Get current metrics and health status
            current_metrics = self.metrics_collector.get_all_current_metrics()
            health_status = self.health_system.get_all_health_status()

            # Check each rule
            for rule in self.alert_rules:
                if not rule.enabled:
                    continue

                # Check each container
                containers_to_check = rule.containers if rule.containers else current_metrics.keys()

                for container_name in containers_to_check:
                    if container_name not in current_metrics:
                        continue

                    metric = current_metrics[container_name]
                    health = health_status.get(container_name, {})

                    # Check if rule condition is met
                    if await self._evaluate_rule_condition(rule, metric, health):
                        await self._handle_alert_triggered(rule, container_name, metric, health)
                    else:
                        await self._handle_alert_resolved(rule, container_name)

            # Save alerts to file
            await self._save_alerts()

        except Exception as e:
            logger.error(f"Error checking alerts: {e}")

    async def _evaluate_rule_condition(
        self, rule: AlertRule, metric: ContainerMetrics, health: Dict[str, Any]
    ) -> bool:
        """Evaluate if a rule condition is met"""
        try:
            # Prepare evaluation context
            context = {
                "cpu_usage_percent": metric.cpu_usage_percent,
                "memory_usage_percent": metric.memory_usage_percent,
                "memory_usage_mb": metric.memory_usage_mb,
                "network_rx_bytes": metric.network_rx_bytes,
                "network_tx_bytes": metric.network_tx_bytes,
                "uptime_seconds": metric.uptime_seconds,
                "restart_count": metric.restart_count,
                "status": metric.status,
                "health_status": health.get("status", "unknown"),
                "threshold_value": rule.threshold_value
            }

            # Evaluate condition
            result = eval(rule.condition, {"__builtins__": {}}, context)
            return bool(result)

        except Exception as e:
            logger.error(f"Error evaluating rule condition '{rule.condition}': {e}")
            return False

    async def _handle_alert_triggered(
        self, rule: AlertRule, container_name: str, metric: ContainerMetrics, health: Dict[str, Any]
    ):
        """Handle when an alert condition is triggered"""
        alert_key = f"{rule.name}:{container_name}"

        # Check cooldown period
        if alert_key in self.last_alert_times:
            time_since_last = datetime.now() - self.last_alert_times[alert_key]
            if time_since_last.total_seconds() < rule.cooldown_minutes * 60:
                return

        # Create or update alert
        if alert_key not in self.active_alerts:
            alert_id = f"{rule.alert_type.value}_{container_name}_{int(datetime.now().timestamp())}"

            alert = Alert(
                id=alert_id,
                alert_type=rule.alert_type,
                severity=rule.severity,
                container_name=container_name,
                message=self._generate_alert_message(rule, container_name, metric, health),
                timestamp=datetime.now(),
                metadata={
                    "rule_name": rule.name,
                    "threshold_value": rule.threshold_value,
                    "current_value": self._get_current_value(rule, metric, health),
                    "container_id": metric.container_id,
                    "container_status": metric.status
                }
            )

            self.active_alerts[alert_key] = alert
            self.alert_history.append(alert)
            self.last_alert_times[alert_key] = datetime.now()

            # Send notification
            await self._send_alert_notification(alert)

            logger.warning(f"🚨 ALERT: {alert.message}")

    async def _handle_alert_resolved(self, rule: AlertRule, container_name: str):
        """Handle when an alert condition is resolved"""
        alert_key = f"{rule.name}:{container_name}"

        if alert_key in self.active_alerts:
            alert = self.active_alerts[alert_key]
            alert.resolved = True
            alert.resolved_timestamp = datetime.now()

            del self.active_alerts[alert_key]

            logger.info(f"✅ RESOLVED: Alert for {container_name} - {rule.name}")

    def _generate_alert_message(
        self, rule: AlertRule, container_name: str, metric: ContainerMetrics, health: Dict[str, Any]
    ) -> str:
        """Generate alert message"""
        current_value = self._get_current_value(rule, metric, health)

        if rule.alert_type == AlertType.HIGH_CPU_USAGE:
            return f"High CPU usage on {container_name}: {current_value:.1f}% (threshold: {rule.threshold_value}%)"
        elif rule.alert_type == AlertType.HIGH_MEMORY_USAGE:
            return f"High memory usage on {container_name}: {current_value:.1f}% (threshold: {rule.threshold_value}%)"
        elif rule.alert_type == AlertType.HEALTH_CHECK_FAILED:
            return f"Health check failed for {container_name}: status is {health.get('status', 'unknown')}"
        elif rule.alert_type == AlertType.CONTAINER_RESTART:
            return f"Container {container_name} has restarted {int(current_value)} times"
        else:
            return f"Alert triggered for {container_name}: {rule.name}"

    def _get_current_value(self, rule: AlertRule, metric: ContainerMetrics, health: Dict[str, Any]) -> float:
        """Get current value for the rule condition"""
        if rule.alert_type == AlertType.HIGH_CPU_USAGE:
            return metric.cpu_usage_percent
        elif rule.alert_type == AlertType.HIGH_MEMORY_USAGE:
            return metric.memory_usage_percent
        elif rule.alert_type == AlertType.CONTAINER_RESTART:
            return float(metric.restart_count)
        elif rule.alert_type == AlertType.HEALTH_CHECK_FAILED:
            return 1.0 if health.get("status") == "unhealthy" else 0.0
        else:
            return 0.0

    async def _send_alert_notification(self, alert: Alert):
        """Send alert notification via email"""
        if not self.email_config.get("enabled", False):
            return

        try:
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_email']
            msg['To'] = ', '.join(self.email_config['to_emails'])
            msg['Subject'] = f"🚨 Container Alert: {alert.severity.value.upper()} - {alert.container_name}"

            # Email body
            body = f"""
Container Alert Notification

Alert ID: {alert.id}
Container: {alert.container_name}
Severity: {alert.severity.value.upper()}
Type: {alert.alert_type.value}
Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

Message: {alert.message}

Metadata:
{json.dumps(alert.metadata, indent=2)}

---
AI Coding Agent Monitoring System
            """

            msg.attach(MIMEText(body, 'plain'))

            # Send email
            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            if self.email_config.get('username'):
                server.starttls()
                server.login(self.email_config['username'], self.email_config['password'])

            server.send_message(msg)
            server.quit()

            logger.info(f"Alert notification sent for {alert.container_name}")

        except Exception as e:
            logger.error(f"Error sending alert notification: {e}")

    async def _save_alerts(self):
        """Save alerts to file"""
        try:
            data = {
                'timestamp': datetime.now().isoformat(),
                'active_alerts': [
                    {
                        **asdict(alert),
                        'timestamp': alert.timestamp.isoformat(),
                        'resolved_timestamp': alert.resolved_timestamp.isoformat() if alert.resolved_timestamp else None,
                        'alert_type': alert.alert_type.value,
                        'severity': alert.severity.value
                    }
                    for alert in self.active_alerts.values()
                ],
                'recent_history': [
                    {
                        **asdict(alert),
                        'timestamp': alert.timestamp.isoformat(),
                        'resolved_timestamp': alert.resolved_timestamp.isoformat() if alert.resolved_timestamp else None,
                        'alert_type': alert.alert_type.value,
                        'severity': alert.severity.value
                    }
                    for alert in self.alert_history[-100:]  # Last 100 alerts
                ]
            }

            with open(self.alerts_file, 'w') as f:
                json.dump(data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving alerts: {e}")

    def add_alert_rule(self, rule: AlertRule):
        """Add a custom alert rule"""
        self.alert_rules.append(rule)
        logger.info(f"Added alert rule: {rule.name}")

    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts"""
        return list(self.active_alerts.values())

    def get_alert_details(self, alert_id: str) -> Optional[Alert]:
        """Get details for a specific alert by ID from active or history"""
        try:
            for alert in self.active_alerts.values():
                if alert.id == alert_id:
                    return alert
            # search in history (latest first)
            for alert in reversed(self.alert_history):
                if alert.id == alert_id:
                    return alert
            return None
        except Exception:
            return None

    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert by ID"""
        alert = self.get_alert_details(alert_id)
        if not alert:
            return False
        alert.acknowledged = True
        return True

    def get_alert_summary(self) -> Dict[str, Any]:
        """Get alert summary"""
        active_alerts = list(self.active_alerts.values())

        return {
            "total_active_alerts": len(active_alerts),
            "critical_alerts": len([a for a in active_alerts if a.severity == AlertSeverity.CRITICAL]),
            "high_alerts": len([a for a in active_alerts if a.severity == AlertSeverity.HIGH]),
            "medium_alerts": len([a for a in active_alerts if a.severity == AlertSeverity.MEDIUM]),
            "low_alerts": len([a for a in active_alerts if a.severity == AlertSeverity.LOW]),
            "total_rules": len(self.alert_rules),
            "enabled_rules": len([r for r in self.alert_rules if r.enabled]),
            "last_check": datetime.now().isoformat()
        }

    async def cleanup_old_alerts(self, days: int = 30):
        """Clean up old alert history"""
        cutoff_time = datetime.now() - timedelta(days=days)

        original_count = len(self.alert_history)
        self.alert_history = [a for a in self.alert_history if a.timestamp >= cutoff_time]
        cleaned_count = original_count - len(self.alert_history)

        if cleaned_count > 0:
            logger.info(f"Cleaned {cleaned_count} old alerts from history")

        logger.info(f"Completed alert cleanup for data older than {days} days")


class MonitoringManager:
    """
    Unified monitoring manager that coordinates all monitoring components.
    Provides a single interface for starting/stopping all monitoring services.
    """

    def __init__(self, dashboard_port: int = 8090):
        """Initialize monitoring manager"""
        self.metrics_collector = ContainerMetricsCollector()
        self.health_system = HealthCheckSystem()
        self.alerting_system = AlertingSystem()

        # Import dashboard here to avoid circular imports
        from monitoring.monitoring_dashboard import MonitoringDashboard
        self.dashboard = MonitoringDashboard(port=dashboard_port)

        self.is_running = False
        logger.info("Initialized MonitoringManager")

    async def start_all_monitoring(self):
        """Start all monitoring components"""
        if self.is_running:
            logger.warning("Monitoring already running")
            return

        try:
            # Start core monitoring systems
            await self.metrics_collector.start_collection()
            await self.health_system.start_monitoring()
            await self.alerting_system.start_monitoring()

            # Start dashboard
            await self.dashboard.start_dashboard()

            self.is_running = True
            logger.info("🚀 All monitoring systems started successfully")
            logger.info(f"📊 Dashboard available at: http://localhost:{self.dashboard.port}")

        except Exception as e:
            logger.error(f"Error starting monitoring systems: {e}")
            await self.stop_all_monitoring()
            raise

    async def stop_all_monitoring(self):
        """Stop all monitoring components"""
        if not self.is_running:
            return

        try:
            # Stop all systems
            await self.dashboard.stop_dashboard()
            await self.alerting_system.stop_monitoring()
            await self.health_system.stop_monitoring()
            await self.metrics_collector.stop_collection()

            self.is_running = False
            logger.info("🛑 All monitoring systems stopped")

        except Exception as e:
            logger.error(f"Error stopping monitoring systems: {e}")

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get status of all monitoring components"""
        return {
            "monitoring_active": self.is_running,
            "metrics_collector": {
                "active": self.metrics_collector.is_collecting,
                "interval_seconds": self.metrics_collector.collection_interval,
                "containers_monitored": len(self.metrics_collector.metrics_history)
            },
            "health_system": {
                "active": self.health_system.is_monitoring,
                "interval_seconds": self.health_system.check_interval,
                "containers_configured": len(self.health_system.health_configs)
            },
            "alerting_system": {
                "active": self.alerting_system.is_monitoring,
                "active_alerts": len(self.alerting_system.active_alerts),
                "alert_rules": len(self.alerting_system.alert_rules)
            },
            "dashboard": {
                "active": self.dashboard.is_running,
                "port": self.dashboard.port,
                "websocket_connections": len(self.dashboard.websocket_connections)
            }
        }
