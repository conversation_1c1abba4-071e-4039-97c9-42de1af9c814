#!/usr/bin/env python3
"""
Test script for API key-based model access
"""

import asyncio
import os
from models.model_router import get_model_router

async def test_api_key_system():
    """Test the API key-based model system"""

    print("🔑 Testing API Key-Based Model System")
    print("=" * 50)

    # Check if OLLAMA_API_KEY is set
    api_key = os.getenv("OLLAMA_API_KEY")
    if api_key:
        print(f"✅ OLLAMA_API_KEY found: {api_key[:8]}...")
    else:
        print("⚠️  OLLAMA_API_KEY not set - will use unauthenticated access")

    # Initialize model router
    router = get_model_router()

    # Test with a simple prompt
    prompt = "Generate a simple Python function to calculate fibonacci numbers"

    print(f"\n🎯 Testing model access with prompt: {prompt[:50]}...")

    # Try to generate a response
    try:
        response = await router.generate_response(
            prompt=prompt,
            task_type="code_generation"
        )

        if response.get("success", False):
            print("✅ Success! Model responded using API key or local access")
            print(f"   📋 Model used: {response.get('model', 'Unknown')}")
            print(f"   ⏱️  Response time: {response.get('response_time', 0):.2f}s")
            content = response.get("content", "")
            print(f"   📄 Content preview: {content[:200]}...")
        else:
            print(f"❌ Failed: {response.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Exception occurred: {e}")

    print("\n🔍 Checking available models...")
    available_models = router.get_available_models()
    for model in available_models:
        health = router.get_model_health(model)
        status = "✅" if health["health"] == "healthy" else "❌"
        print(f"   {status} {model}: {health['health']}")

if __name__ == "__main__":
    asyncio.run(test_api_key_system())
