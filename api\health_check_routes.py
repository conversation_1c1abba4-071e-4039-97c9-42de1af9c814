# api/health_check_routes.py
"""
Health Check API Routes
Provides REST API endpoints for container health monitoring and configuration.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from api.agent_dependency import get_agent
from monitoring.health_check_system import (
    HealthCheckSystem,
    HealthCheckConfig,
    HealthStatus,
)

router = APIRouter(prefix="/api/health", tags=["Health Checks"])


class HealthResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str
    error: Optional[str] = None


@router.get("/containers", response_model=HealthResponse)
async def list_containers_health(agent=Depends(get_agent)):
    """Get health status for all containers"""
    try:
        health_system = HealthCheckSystem()
        results = await health_system.check_all_containers()

        containers = {
            name: {
                "container_name": result.container_name,
                "status": result.status.value,
                "response_time_ms": result.response_time_ms,
                "endpoint": result.endpoint,
                "timestamp": result.timestamp.isoformat(),
                "error_message": result.error_message,
            }
            for name, result in results.items()
        }

        summary = {
            "total_containers": len(results),
            "healthy": len([r for r in results.values() if r.status == HealthStatus.HEALTHY]),
            "unhealthy": len([r for r in results.values() if r.status == HealthStatus.UNHEALTHY]),
            "starting": len([r for r in results.values() if r.status == HealthStatus.STARTING]),
            "unknown": len([r for r in results.values() if r.status == HealthStatus.UNKNOWN]),
        }

        return HealthResponse(
            success=True,
            message="Container health status retrieved",
            data={"containers": containers, "summary": summary},
            timestamp=datetime.now().isoformat(),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


class HealthConfigRequest(BaseModel):
    endpoint: str = "/"
    port: int = 80
    timeout_seconds: int = 10
    interval_seconds: int = 30
    retries: int = 3
    start_period_seconds: int = 40
    expected_status_codes: Optional[List[int]] = None


@router.post("/configure/{container_name}", response_model=HealthResponse)
async def configure_container_health(
    container_name: str, request: HealthConfigRequest, agent=Depends(get_agent)
):
    """Configure health check for a specific container"""
    try:
        health_system = HealthCheckSystem()

        config = HealthCheckConfig(
            container_name=container_name,
            endpoint=request.endpoint,
            port=request.port,
            timeout_seconds=request.timeout_seconds,
            interval_seconds=request.interval_seconds,
            retries=request.retries,
            start_period_seconds=request.start_period_seconds,
            expected_status_codes=request.expected_status_codes or [200, 301, 302],
        )

        health_system.configure_health_check(container_name, config)

        return HealthResponse(
            success=True,
            message=f"Configured health check for {container_name}",
            data={"container_name": container_name, "config": config.__dict__},
            timestamp=datetime.now().isoformat(),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/containers/{container_name}", response_model=HealthResponse)
async def get_container_health(
    container_name: str, minutes: int = Query(60), agent=Depends(get_agent)
):
    """Get detailed health summary for a specific container"""
    try:
        health_system = HealthCheckSystem()
        summary = health_system.get_health_summary(container_name, minutes)

        if "error" in summary:
            raise HTTPException(status_code=404, detail=summary["error"])

        return HealthResponse(
            success=True,
            message=f"Health summary retrieved for {container_name}",
            data=summary,
            timestamp=datetime.now().isoformat(),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/summary", response_model=HealthResponse)
async def get_health_summary(agent=Depends(get_agent)):
    """Get overall health summary across all containers"""
    try:
        health_system = HealthCheckSystem()
        # Collect all containers' latest health and compute summary
        results = await health_system.check_all_containers()

        summary = {
            "total_containers": len(results),
            "healthy": len([r for r in results.values() if r.status == HealthStatus.HEALTHY]),
            "unhealthy": len([r for r in results.values() if r.status == HealthStatus.UNHEALTHY]),
            "starting": len([r for r in results.values() if r.status == HealthStatus.STARTING]),
            "unknown": len([r for r in results.values() if r.status == HealthStatus.UNKNOWN]),
        }

        return HealthResponse(
            success=True,
            message="Overall health summary retrieved",
            data=summary,
            timestamp=datetime.now().isoformat(),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

