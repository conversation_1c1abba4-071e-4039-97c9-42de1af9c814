#!/usr/bin/env python3
"""
Test script for Ollama API
"""

import asyncio
import aiohttp
import json

async def test_ollama_api():
    """Test Ollama API directly"""

    print("🔍 Testing Ollama API Directly")
    print("=" * 40)

    base_url = "http://localhost:11434"

    # Test 1: Check available models
    print("\n1. Checking available models...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ API response: {data}")
                    models = data.get("models", [])
                    if models:
                        print(f"   📋 Found {len(models)} models:")
                        for model in models:
                            print(f"      - {model.get('name', 'Unknown')}")
                    else:
                        print("   ⚠️  No models found in API response")
                else:
                    print(f"   ❌ API error: {response.status}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 2: Try to generate a response
    print("\n2. Testing model generation...")
    test_models = ["starcoder2:3b", "deepseek-coder:6.7b-instruct", "yi-coder:1.5b"]

    for model_name in test_models:
        print(f"\n   🎯 Testing model: {model_name}")

        payload = {
            "model": model_name,
            "prompt": "Hello, can you help me with coding?",
            "stream": False
        }

        try:
            async with aiohttp.ClientSession() as session:
                print(f"   📤 Sending request to {base_url}/api/generate")
                print(f"   📄 Payload: {json.dumps(payload, indent=2)}")

                async with session.post(f"{base_url}/api/generate", json=payload, timeout=30) as response:
                    print(f"   📥 Response status: {response.status}")

                    if response.status == 200:
                        data = await response.json()
                        print(f"   ✅ Success! Response: {data.get('response', '')[:100]}...")
                        break
                    else:
                        error_text = await response.text()
                        print(f"   ❌ Failed ({response.status}): {error_text}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ollama_api())
