# core/site_container_manager.py
"""
Site Container Manager
Handles individual Docker containers for each website with isolation and management capabilities.
Enhanced with SSL support, monitoring, hot reload, and comprehensive management features.

NOTE: This is the specialized website container manager.
For general Docker operations, use containerization/docker_manager.py
"""

import asyncio
import json
import logging
import os
import shutil
import socket
import ssl
import subprocess
import tempfile
import time
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import aiohttp
import docker
from docker import errors as docker_errors
import psutil
import yaml

logger = logging.getLogger(__name__)


class ContainerStatus(Enum):
    """Container status enumeration"""

    STOPPED = "stopped"
    RUNNING = "running"
    STARTING = "starting"
    STOPPING = "stopping"
    ERROR = "error"
    BUILDING = "building"
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"


class EnvironmentType(Enum):
    """Environment type enumeration"""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class SiteContainer:
    """Site container configuration and status"""

    site_name: str
    container_name: str
    port: int
    status: ContainerStatus
    image_name: str
    created_at: datetime
    environment: EnvironmentType = EnvironmentType.DEVELOPMENT
    last_started: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    health_status: str = "unknown"
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    logs: List[str] = field(default_factory=list)
    ssl_enabled: bool = False
    ssl_cert_path: Optional[str] = None
    backup_enabled: bool = True
    last_backup: Optional[datetime] = None
    hot_reload_enabled: bool = False
    monitoring_enabled: bool = True


class PortManager:
    """Enhanced port allocation with persistence and conflict resolution"""

    def __init__(
        self,
        start_port: int = 8080,
        end_port: int = 9000,
        registry_file: str = "config/port_registry.json",
    ):
        self.start_port = start_port
        self.end_port = end_port
        self.allocated_ports = set()
        self.port_assignments = {}  # site_name -> port
        self.registry_file = Path(registry_file)
        self.registry_file.parent.mkdir(exist_ok=True)
        self._load_registry()

    def _load_registry(self):
        """Load port registry from disk"""
        if self.registry_file.exists():
            try:
                with open(self.registry_file, "r") as f:
                    data = json.load(f)
                    self.port_assignments = data.get("assignments", {})
                    self.allocated_ports = set(data.get("allocated", []))
                logger.info(
                    f"Loaded port registry with {len(self.port_assignments)} assignments"
                )
            except Exception as e:
                logger.error(f"Failed to load port registry: {e}")

    def _save_registry(self):
        """Save port registry to disk"""
        try:
            data = {
                "assignments": self.port_assignments,
                "allocated": list(self.allocated_ports),
                "last_updated": datetime.now().isoformat(),
            }
            with open(self.registry_file, "w") as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save port registry: {e}")

    def allocate_port(self, site_name: str) -> int:
        """Allocate a port for a site with conflict resolution"""
        if site_name in self.port_assignments:
            return self.port_assignments[site_name]

        for port in range(self.start_port, self.end_port):
            if port not in self.allocated_ports and not self._is_port_in_use(port):
                self.allocated_ports.add(port)
                self.port_assignments[site_name] = port
                self._save_registry()
                logger.info(f"Allocated port {port} for site {site_name}")
                return port

        raise RuntimeError(
            f"No available ports in range {self.start_port}-{self.end_port}"
        )

    def _is_port_in_use(self, port: int) -> bool:
        """Check if a port is currently in use"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return False
        except OSError:
            return True

    def release_port(self, site_name: str) -> None:
        """Release a port allocation"""
        if site_name in self.port_assignments:
            port = self.port_assignments[site_name]
            self.allocated_ports.discard(port)
            del self.port_assignments[site_name]
            self._save_registry()
            logger.info(f"Released port {port} for site {site_name}")

    def get_site_port(self, site_name: str) -> Optional[int]:
        """Get the allocated port for a site"""
        return self.port_assignments.get(site_name)

    def list_allocations(self) -> Dict[str, int]:
        """List all port allocations"""
        return self.port_assignments.copy()

    def get_available_ports(self) -> List[int]:
        """Get list of available ports in the range"""
        available = []
        for port in range(self.start_port, self.end_port):
            if port not in self.allocated_ports and not self._is_port_in_use(port):
                available.append(port)
        return available


class NginxManager:
    """Manages Nginx configuration for site containers"""

    def __init__(
        self,
        nginx_conf_dir: str = "nginx",
        sites_enabled_dir: str = "nginx/sites-enabled",
    ):
        self.nginx_conf_dir = Path(nginx_conf_dir)
        self.sites_enabled_dir = Path(sites_enabled_dir)
        self.nginx_conf_dir.mkdir(exist_ok=True)
        self.sites_enabled_dir.mkdir(exist_ok=True)

    async def add_site_config(
        self, site_name: str, port: int, ssl_enabled: bool = False
    ) -> Dict[str, Any]:
        """Add Nginx configuration for a site"""
        try:
            config_content = self._generate_site_config(site_name, port, ssl_enabled)
            config_file = self.sites_enabled_dir / f"{site_name}.conf"

            with open(config_file, "w") as f:
                f.write(config_content)

            await self._reload_nginx()

            return {
                "success": True,
                "message": f"Nginx config added for {site_name}",
                "config_file": str(config_file),
            }
        except Exception as e:
            logger.error(f"Failed to add Nginx config for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    def _generate_site_config(
        self, site_name: str, port: int, ssl_enabled: bool
    ) -> str:
        """Generate Nginx configuration for a site"""
        if ssl_enabled:
            return f"""
server {{
    listen 80;
    server_name {site_name}.localhost;
    return 301 https://$server_name$request_uri;
}}

server {{
    listen 443 ssl;
    server_name {site_name}.localhost;

    ssl_certificate /etc/ssl/certs/{site_name}.crt;
    ssl_certificate_key /etc/ssl/private/{site_name}.key;

    location / {{
        proxy_pass http://localhost:{port};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
}}
"""
        else:
            return f"""
server {{
    listen 80;
    server_name {site_name}.localhost;

    location / {{
        proxy_pass http://localhost:{port};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
}}
"""

    async def remove_site_config(self, site_name: str) -> Dict[str, Any]:
        """Remove Nginx configuration for a site"""
        try:
            config_file = self.sites_enabled_dir / f"{site_name}.conf"
            if config_file.exists():
                config_file.unlink()
                await self._reload_nginx()

            return {"success": True, "message": f"Nginx config removed for {site_name}"}
        except Exception as e:
            logger.error(f"Failed to remove Nginx config for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def _reload_nginx(self):
        """Reload Nginx configuration"""
        try:
            subprocess.run(["nginx", "-s", "reload"], check=True, capture_output=True)
            logger.info("Nginx configuration reloaded")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to reload Nginx: {e}")


class DockerfileGenerator:
    """Generates Dockerfiles for different site types"""

    def __init__(self, templates_dir: str = "templates/docker"):
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(parents=True, exist_ok=True)

    async def generate_dockerfile(
        self, site_name: str, site_path: Path, environment: EnvironmentType
    ) -> Path:
        """Generate appropriate Dockerfile for a site"""
        framework = self._detect_framework(site_path)
        dockerfile_path = site_path / "Dockerfile"

        if framework == "nextjs":
            content = self._generate_nextjs_dockerfile(site_name, environment)
        elif framework == "react":
            content = self._generate_react_dockerfile(site_name, environment)
        elif framework == "static":
            content = self._generate_static_dockerfile(site_name, environment)
        else:
            content = self._generate_generic_dockerfile(site_name, environment)

        with open(dockerfile_path, "w") as f:
            f.write(content)

        return dockerfile_path

    def _detect_framework(self, site_path: Path) -> str:
        """Detect the framework used by the site"""
        if (site_path / "next.config.js").exists() or (
            site_path / "next.config.ts"
        ).exists():
            return "nextjs"
        elif (site_path / "package.json").exists():
            with open(site_path / "package.json", "r") as f:
                package_data = json.load(f)
                dependencies = package_data.get("dependencies", {})
                if "react" in dependencies and "next" not in dependencies:
                    return "react"
        elif (site_path / "index.html").exists():
            return "static"
        return "generic"

    def _detect_site_characteristics(self, site_path: Path) -> Dict[str, Any]:
        """Detect site type and optimize build strategy"""
        import json

        characteristics = {
            "framework": "static",
            "needs_build": False,
            "build_output_dir": None,
            "runtime_files": [],
            "dev_files": [],
            "package_manager": None,
            "has_dependencies": False
        }

        # Check for package.json (Node.js projects)
        if (site_path / "package.json").exists():
            characteristics["has_dependencies"] = True
            characteristics["package_manager"] = "npm"

            try:
                with open(site_path / "package.json", "r") as f:
                    package_data = json.load(f)

                dependencies = package_data.get("dependencies", {})
                dev_dependencies = package_data.get("devDependencies", {})
                scripts = package_data.get("scripts", {})

                # Detect React
                if "react" in dependencies:
                    characteristics["framework"] = "react"
                    characteristics["needs_build"] = "build" in scripts
                    characteristics["build_output_dir"] = "build"
                    characteristics["dev_files"].extend(["src/", "public/"])

                # Detect Next.js
                if "next" in dependencies:
                    characteristics["framework"] = "nextjs"
                    characteristics["needs_build"] = True
                    characteristics["build_output_dir"] = ".next"
                    characteristics["dev_files"].extend(["pages/", "components/", "styles/"])

                # Detect Vue
                if "vue" in dependencies:
                    characteristics["framework"] = "vue"
                    characteristics["needs_build"] = "build" in scripts
                    characteristics["build_output_dir"] = "dist"

            except (json.JSONDecodeError, FileNotFoundError):
                pass

        # Check for requirements.txt (Python projects)
        if (site_path / "requirements.txt").exists():
            characteristics["has_dependencies"] = True
            characteristics["package_manager"] = "pip"
            characteristics["framework"] = "python"

            # Check for common Python web frameworks
            try:
                with open(site_path / "requirements.txt", "r") as f:
                    requirements = f.read().lower()
                    if "flask" in requirements:
                        characteristics["framework"] = "flask"
                    elif "django" in requirements:
                        characteristics["framework"] = "django"
                    elif "fastapi" in requirements:
                        characteristics["framework"] = "fastapi"
            except FileNotFoundError:
                pass

        # Check for static files
        static_files = list(site_path.glob("*.html")) + list(site_path.glob("*.css")) + list(site_path.glob("*.js"))
        if static_files and not characteristics["has_dependencies"]:
            characteristics["framework"] = "static"
            characteristics["runtime_files"] = [f.name for f in static_files]

        # Add common dev files to ignore
        characteristics["dev_files"].extend([
            "node_modules/", "__pycache__/", ".git/", ".vscode/",
            "*.log", "*.tmp", ".env*", "!.env.example",
            "tests/", "__tests__/", "*.test.*", "coverage/",
            "docs/", "README.md", "*.md"
        ])

        return characteristics

    def _generate_nextjs_dockerfile(
        self, site_name: str, environment: EnvironmentType
    ) -> str:
        """Generate Dockerfile for Next.js applications"""
        if environment == EnvironmentType.PRODUCTION:
            return f"""
# Multi-stage build for Next.js production
FROM node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d AS runner
WORKDIR /app
ENV NODE_ENV=production
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
EXPOSE 3000
CMD ["node", "server.js"]
"""
        else:
            return f"""
# Development Dockerfile for Next.js
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]
"""

    def _generate_react_dockerfile(
        self, site_name: str, environment: EnvironmentType
    ) -> str:
        """Generate Dockerfile for React applications"""
        if environment == EnvironmentType.PRODUCTION:
            return f"""
# Multi-stage build for React production
FROM node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:1.25-alpine@sha256:2d194184b067db3598771b4cf326cfe6ad5051937ba2cc83222d94d4cd9c81bd AS runner
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
"""
        else:
            return f"""
# Development Dockerfile for React
FROM node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
"""

    def _generate_static_dockerfile(
        self, site_name: str, environment: EnvironmentType
    ) -> str:
        """Generate Dockerfile for static sites"""
        return f"""
# Static site Dockerfile
FROM nginx:alpine
COPY . /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
"""

    def _generate_generic_dockerfile(
        self, site_name: str, environment: EnvironmentType
    ) -> str:
        """Generate generic Dockerfile"""
        return f"""
# Generic Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "app.py"]
"""


class SiteContainerManager:
    """Manages individual Docker containers for each website"""

    def __init__(self, sites_dir: str = "sites", containers_dir: str = "containers"):
        """Initialize the SiteContainerManager"""
        self.sites_dir = Path(sites_dir)
        self.containers_dir = Path(containers_dir)
        self.port_manager = PortManager()
        self.nginx_manager = NginxManager()
        self.dockerfile_generator = DockerfileGenerator()

        # Initialize Docker client only if Docker socket is available
        self.docker_client = None
        try:
            self.docker_client = docker.from_env()
            logger.info("Docker client initialized successfully")
        except Exception as e:
            logger.warning(
                f"Docker client not available: {e}. Container operations will be limited."
            )

        self.site_containers: Dict[str, SiteContainer] = {}

        # Ensure directories exist
        self.sites_dir.mkdir(exist_ok=True)
        self.containers_dir.mkdir(exist_ok=True)

        # Load existing container states
        self._load_container_states()

        logger.info("Enhanced SiteContainerManager initialized")

    def _load_container_states(self):
        """Load existing container states from disk"""
        state_file = self.containers_dir / "container_states.json"
        if state_file.exists():
            try:
                with open(state_file, "r") as f:
                    data = json.load(f)

                # Restore port allocations
                for site_name, port in data.get("port_assignments", {}).items():
                    self.port_manager.port_assignments[site_name] = port
                    self.port_manager.allocated_ports.add(port)

                # Restore container states
                for site_data in data.get("containers", []):
                    container = SiteContainer(**site_data)
                    self.site_containers[container.site_name] = container

                logger.info(f"Loaded {len(self.site_containers)} container states")
            except Exception as e:
                logger.error(f"Failed to load container states: {e}")

    def _save_container_states(self):
        """Save container states to disk"""
        state_file = self.containers_dir / "container_states.json"
        try:
            data = {
                "port_assignments": self.port_manager.port_assignments,
                "containers": [
                    asdict(container) for container in self.site_containers.values()
                ],
            }
            with open(state_file, "w") as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save container states: {e}")

    async def create_site_container(
        self, site_name: str, site_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a Docker container for a website"""
        try:
            logger.info(f"Creating container for site: {site_name}")

            # Check if site exists
            site_path = self.sites_dir / site_name
            if not site_path.exists():
                return {
                    "success": False,
                    "error": f"Site {site_name} does not exist at {site_path}",
                }

            # Allocate port
            port = self.port_manager.allocate_port(site_name)

            # Detect site characteristics for optimization
            characteristics = self._detect_site_characteristics(site_path)

            # Create container configuration
            container_name = f"site-{site_name}"
            image_name = f"ai-coding-site-{site_name}"

            # Create optimized .dockerignore
            dockerignore_path = await self._create_site_dockerignore(site_path, characteristics)

            # Create Dockerfile for the site
            dockerfile_path = await self._create_site_dockerfile(site_name, site_path)

            # Create site-specific environment file
            env_file_path = await self._create_site_env_file(site_name, port)

            # Create docker-compose for the site
            compose_path = await self._create_site_compose(site_name, port, image_name)

            # Build the container image
            build_result = await self._build_site_image(
                site_name, image_name, dockerfile_path
            )
            if not build_result["success"]:
                self.port_manager.release_port(site_name)
                return build_result

            # Create container record
            container = SiteContainer(
                site_name=site_name,
                container_name=container_name,
                port=port,
                status=ContainerStatus.STOPPED,
                image_name=image_name,
                created_at=datetime.now(),
                resource_usage={},
                logs=[],
            )

            self.site_containers[site_name] = container
            self._save_container_states()

            logger.info(f"Container created for site {site_name} on port {port}")

            return {
                "success": True,
                "container": asdict(container),
                "port": port,
                "dockerfile": str(dockerfile_path),
                "compose": str(compose_path),
            }

        except Exception as e:
            logger.error(f"Failed to create container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def _create_site_dockerfile(self, site_name: str, site_path: Path) -> Path:
        """Create a Dockerfile for a site"""
        dockerfile_path = self.containers_dir / f"Dockerfile.{site_name}"

        # Determine if it's a static site or needs a server
        has_server_files = any(site_path.glob("*.py")) or any(
            site_path.glob("package.json")
        )

        if has_server_files:
            # Dynamic site with server
            dockerfile_content = f"""
FROM python:3.11-slim@sha256:f2ee145f3bc4e061f8dfe7e6ebd427a410121495a0bd26e7622136db060b7e8e

# Create non-root user early
RUN groupadd --gid 1000 appuser && \\
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy site files with proper ownership
COPY --chown=appuser:appuser {site_name}/ .

# Install Python dependencies if requirements.txt exists
RUN if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

# Install Node.js dependencies if package.json exists
RUN if [ -f package.json ]; then npm install; fi

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

# Start command
CMD ["python", "-m", "http.server", "80", "--bind", "0.0.0.0"]
"""
        else:
            # Static site
            dockerfile_content = f"""
FROM nginx:1.25-alpine@sha256:2d194184b067db3598771b4cf326cfe6ad5051937ba2cc83222d94d4cd9c81bd

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup --system --gid 1000 appuser && \\
    adduser --system --uid 1000 --ingroup appuser appuser

# Copy site files with proper ownership
COPY --chown=appuser:appuser {site_name}/ /usr/share/nginx/html/

# Copy custom nginx config if exists
COPY {site_name}/nginx.conf /etc/nginx/conf.d/default.conf 2>/dev/null || true

# Configure nginx for non-root
RUN chown -R appuser:appuser /var/cache/nginx /var/log/nginx /etc/nginx/conf.d \\
    && touch /var/run/nginx.pid \\
    && chown appuser:appuser /var/run/nginx.pid

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
"""

        with open(dockerfile_path, "w") as f:
            f.write(dockerfile_content)

        return dockerfile_path

    async def _create_site_dockerignore(self, site_path: Path, characteristics: Dict[str, Any]) -> Path:
        """Generate optimized .dockerignore for the site"""
        dockerignore_path = site_path / ".dockerignore"

        ignore_patterns = [
            "# AI Coding Agent - Auto-generated .dockerignore",
            "node_modules/", "__pycache__/", ".git/", ".vscode/",
            "*.log", "*.tmp", ".env*", "!.env.example",
            "tests/", "__tests__/", "*.test.*", "coverage/",
            "docs/", "README.md", "*.md"
        ]

        # Add framework-specific ignores
        framework = characteristics.get("framework", "static")
        if framework == "nextjs":
            ignore_patterns.extend([".next/", "out/", "coverage/", ".vercel/"])
        elif framework == "react":
            ignore_patterns.extend(["build/", "dist/", "coverage/", ".cache/"])
        elif framework in ["python", "flask", "django", "fastapi"]:
            ignore_patterns.extend(["__pycache__/", "*.pyc", ".pytest_cache/", "venv/", ".venv/"])
        elif framework == "vue":
            ignore_patterns.extend(["dist/", "node_modules/", ".cache/"])

        # Add development files from characteristics
        dev_files = characteristics.get("dev_files", [])
        ignore_patterns.extend(dev_files)

        # Remove duplicates while preserving order
        seen = set()
        unique_patterns = []
        for pattern in ignore_patterns:
            if pattern not in seen:
                seen.add(pattern)
                unique_patterns.append(pattern)

        with open(dockerignore_path, "w") as f:
            f.write("\n".join(unique_patterns))

        logger.info(f"Generated .dockerignore for {site_path.name} with {len(unique_patterns)} patterns")
        return dockerignore_path

    async def _create_site_env_file(self, site_name: str, port: int) -> Path:
        """Create site-specific environment file"""
        env_file_path = Path(f".env.{site_name}")

        env_content = f"""# Site-specific environment for {site_name}
SITE_NAME={site_name}
CONTAINER_PORT=80
EXTERNAL_PORT={port}
ENVIRONMENT=production
BUILD_TIMESTAMP={datetime.now().isoformat()}
LOG_LEVEL=info
HEALTH_CHECK_INTERVAL=30s
"""

        with open(env_file_path, "w") as f:
            f.write(env_content)

        logger.info(f"Created environment file for {site_name}: {env_file_path}")
        return env_file_path

    async def _create_site_compose(
        self, site_name: str, port: int, image_name: str
    ) -> Path:
        """Create docker-compose file for a site"""
        compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"

        compose_config = {
            "version": "3.8",
            "services": {
                site_name: {
                    "build": {
                        "context": str(self.sites_dir),
                        "dockerfile": f"../containers/Dockerfile.{site_name}",
                    },
                    "container_name": f"site-{site_name}",
                    "restart": "unless-stopped",
                    "ports": [f"{port}:80"],
                    "volumes": [
                        f"./{site_name}:/app/{site_name}:ro",
                        f"./logs:/app/logs",
                    ],
                    "networks": ["ai-coding-network"],
                    "env_file": [".env", f".env.{site_name}"],
                    "deploy": {
                        "resources": {
                            "limits": {
                                "cpus": "1.0",
                                "memory": "512M"
                            },
                            "reservations": {
                                "cpus": "0.25",
                                "memory": "128M"
                            }
                        }
                    },
                    "healthcheck": {
                        "test": ["CMD", "curl", "-f", "http://localhost:80/"],
                        "interval": "30s",
                        "timeout": "10s",
                        "retries": 3,
                        "start_period": "40s",
                    },
                    "logging": {
                        "driver": "json-file",
                        "options": {
                            "max-size": "10m",
                            "max-file": "3"
                        }
                    },
                }
            },
            "networks": {"ai-coding-network": {"external": True}},
        }

        with open(compose_path, "w") as f:
            yaml.dump(compose_config, f, default_flow_style=False)

        return compose_path

    async def _build_site_image(
        self, site_name: str, image_name: str, dockerfile_path: Path
    ) -> Dict[str, Any]:
        """Build Docker image for a site using the build script"""
        try:
            logger.info(f"Building image for site: {site_name}")

            # Determine the build script to use based on platform
            import platform

            if platform.system() == "Windows":
                build_script = "containers/build.bat"
            else:
                build_script = "containers/build.sh"

            # Make the script executable on Unix systems
            if platform.system() != "Windows":
                import os

                os.chmod(build_script, 0o755)

            # Build the image using the build script
            result = subprocess.run(
                [build_script, site_name, image_name, "latest"],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                logger.info(f"Successfully built image {image_name}")
                return {"success": True}
            else:
                logger.error(f"Failed to build image: {result.stderr}")
                return {"success": False, "error": result.stderr}

        except Exception as e:
            logger.error(f"Error building image for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def start_site_container(self, site_name: str) -> Dict[str, Any]:
        """Start a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"

            if not compose_path.exists():
                return {
                    "success": False,
                    "error": f"Docker compose file not found for {site_name}",
                }

            # Start the container
            result = subprocess.run(
                ["docker-compose", "-f", str(compose_path), "up", "-d"],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                container.status = ContainerStatus.RUNNING
                container.last_started = datetime.now()
                self._save_container_states()

                logger.info(
                    f"Started container for site {site_name} on port {container.port}"
                )

                return {
                    "success": True,
                    "container": asdict(container),
                    "url": f"http://localhost:{container.port}",
                }
            else:
                container.status = ContainerStatus.ERROR
                self._save_container_states()

                return {"success": False, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to start container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def stop_site_container(self, site_name: str) -> Dict[str, Any]:
        """Stop a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"

            if not compose_path.exists():
                return {
                    "success": False,
                    "error": f"Docker compose file not found for {site_name}",
                }

            # Stop the container
            result = subprocess.run(
                ["docker-compose", "-f", str(compose_path), "down"],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                container.status = ContainerStatus.STOPPED
                self._save_container_states()

                logger.info(f"Stopped container for site {site_name}")

                return {"success": True, "container": asdict(container)}
            else:
                return {"success": False, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to stop container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def delete_site_container(self, site_name: str) -> Dict[str, Any]:
        """Delete a site container and its resources"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"

            # Stop and remove container
            if compose_path.exists():
                subprocess.run(
                    [
                        "docker-compose",
                        "-f",
                        str(compose_path),
                        "down",
                        "--rmi",
                        "all",
                        "--volumes",
                    ],
                    capture_output=True,
                )

            # Remove Docker image
            if self.docker_client:
                try:
                    self.docker_client.images.remove(container.image_name, force=True)
                except:
                    pass  # Image might not exist
            else:
                logger.warning("Docker client not available, skipping image removal")

            # Remove files
            dockerfile_path = self.containers_dir / f"Dockerfile.{site_name}"
            if dockerfile_path.exists():
                dockerfile_path.unlink()

            if compose_path.exists():
                compose_path.unlink()

            # Release port
            self.port_manager.release_port(site_name)

            # Remove container record
            del self.site_containers[site_name]
            self._save_container_states()

            logger.info(f"Deleted container for site {site_name}")

            return {
                "success": True,
                "message": f"Container for site {site_name} deleted successfully",
            }

        except Exception as e:
            logger.error(f"Failed to delete container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_status(self, site_name: str) -> Dict[str, Any]:
        """Get the status of a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]

            # Get Docker container status
            if self.docker_client:
                try:
                    docker_container = self.docker_client.containers.get(
                        container.container_name
                    )
                    container.status = (
                        ContainerStatus.RUNNING
                        if docker_container.status == "running"
                        else ContainerStatus.STOPPED
                    )
                    container.health_status = (
                        docker_container.attrs.get("State", {})
                        .get("Health", {})
                        .get("Status", "unknown")
                    )

                    # Get resource usage
                    stats = docker_container.stats(stream=False)
                    container.resource_usage = {
                        "cpu_percent": stats.get("cpu_stats", {})
                        .get("cpu_usage", {})
                        .get("total_usage", 0),
                        "memory_usage": stats.get("memory_stats", {}).get("usage", 0),
                        "memory_limit": stats.get("memory_stats", {}).get("limit", 0),
                    }

                except docker_errors.NotFound:
                    container.status = ContainerStatus.STOPPED
                    container.health_status = "not_found"
                    container.resource_usage = {}
            else:
                # Docker client not available, use stored status
                logger.warning(
                    "Docker client not available, using stored container status"
                )
                container.health_status = "unknown"
                container.resource_usage = {}

            self._save_container_states()

            return {"success": True, "container": asdict(container)}

        except Exception as e:
            logger.error(f"Failed to get status for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def list_containers(self) -> Dict[str, Any]:
        """List all site containers"""
        try:
            containers = []
            for site_name, container in self.site_containers.items():
                # Update status
                status_result = await self.get_container_status(site_name)
                if status_result["success"]:
                    containers.append(status_result["container"])

            return {"success": True, "containers": containers, "total": len(containers)}

        except Exception as e:
            logger.error(f"Failed to list containers: {e}")
            return {"success": False, "error": str(e)}

    async def rebuild_site_container(self, site_name: str) -> Dict[str, Any]:
        """Rebuild a site container"""
        try:
            # Stop and delete existing container
            await self.stop_site_container(site_name)
            await self.delete_site_container(site_name)

            # Recreate container
            site_config = {"name": site_name}  # Basic config, can be enhanced
            create_result = await self.create_site_container(site_name, site_config)

            if create_result["success"]:
                # Start the new container
                start_result = await self.start_site_container(site_name)
                return start_result
            else:
                return create_result

        except Exception as e:
            logger.error(f"Failed to rebuild container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_logs(
        self, site_name: str, lines: int = 100
    ) -> Dict[str, Any]:
        """Get logs from a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]

            if self.docker_client:
                try:
                    docker_container = self.docker_client.containers.get(
                        container.container_name
                    )
                    logs = docker_container.logs(tail=lines, timestamps=True).decode(
                        "utf-8"
                    )

                    return {
                        "success": True,
                        "logs": logs.split("\n"),
                        "container_name": container.container_name,
                    }

                except docker_errors.NotFound:
                    return {"success": False, "error": "Container not found in Docker"}
            else:
                logger.warning(
                    "Docker client not available, cannot retrieve container logs"
                )
                return {"success": False, "error": "Docker client not available"}

        except Exception as e:
            logger.error(f"Failed to get logs for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    # Enhanced methods
    async def backup_site_container(self, site_name: str) -> Dict[str, Any]:
        """Backup a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            backup_dir = Path("backups") / site_name
            backup_dir.mkdir(parents=True, exist_ok=True)

            # Create backup timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = backup_dir / f"backup_{timestamp}.tar"

            # Create Docker container backup
            if self.docker_client:
                try:
                    docker_container = self.docker_client.containers.get(
                        container.container_name
                    )
                    backup_stream = docker_container.export()

                    with open(backup_path, "wb") as f:
                        for chunk in backup_stream:
                            if isinstance(chunk, str):
                                f.write(chunk.encode('utf-8'))
                            else:
                                f.write(chunk)

                    # Update container backup info
                    container.last_backup = datetime.now()
                    self._save_container_states()

                    return {
                        "success": True,
                        "message": f"Backup created for {site_name}",
                        "backup_path": str(backup_path),
                        "backup_size": backup_path.stat().st_size,
                    }

                except docker_errors.NotFound:
                    return {"success": False, "error": "Container not found in Docker"}
            else:
                logger.warning(
                    "Docker client not available, cannot create container backup"
                )
                return {"success": False, "error": "Docker client not available"}

        except Exception as e:
            logger.error(f"Failed to backup site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def enable_hot_reload(self, site_name: str) -> Dict[str, Any]:
        """Enable hot reload for a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            container.hot_reload_enabled = True
            self._save_container_states()

            # Update docker-compose with volume mounts for hot reload
            compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"
            if compose_path.exists():
                with open(compose_path, "r") as f:
                    compose_data = yaml.safe_load(f)

                # Add volume mounts for hot reload
                if "services" in compose_data and site_name in compose_data["services"]:
                    service = compose_data["services"][site_name]
                    service["volumes"] = [
                        f"./sites/{site_name}:/app",
                        "/app/node_modules",  # Preserve node_modules
                    ]

                with open(compose_path, "w") as f:
                    yaml.dump(compose_data, f)

            return {"success": True, "message": f"Hot reload enabled for {site_name}"}

        except Exception as e:
            logger.error(f"Failed to enable hot reload for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def configure_ssl(
        self, site_name: str, ssl_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure SSL for a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            container.ssl_enabled = True
            container.ssl_cert_path = ssl_config.get("cert_path")
            self._save_container_states()

            # Add Nginx SSL configuration
            nginx_result = await self.nginx_manager.add_site_config(
                site_name, container.port, ssl_enabled=True
            )

            if nginx_result["success"]:
                return {
                    "success": True,
                    "message": f"SSL configured for {site_name}",
                    "nginx_config": nginx_result,
                }
            else:
                return nginx_result

        except Exception as e:
            logger.error(f"Failed to configure SSL for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def _perform_health_check(self, site_name: str, container: SiteContainer):
        """Perform health check on a container"""
        try:
            logger.debug(f"Performing health check for {site_name}")
            # Check if container is responding
            async with aiohttp.ClientSession() as session:
                url = f"http://localhost:{container.port}/health"
                timeout = aiohttp.ClientTimeout(total=5)
                async with session.get(url, timeout=timeout) as response:
                    if response.status == 200:
                        container.health_status = "healthy"
                        container.status = ContainerStatus.HEALTHY
                    else:
                        container.health_status = "unhealthy"
                        container.status = ContainerStatus.UNHEALTHY
        except Exception:
            container.health_status = "unhealthy"
            container.status = ContainerStatus.UNHEALTHY

        container.last_health_check = datetime.now()
        self._save_container_states()

    def _calculate_cpu_percent(self, stats: Dict[str, Any]) -> float:
        """Calculate CPU usage percentage from Docker stats"""
        try:
            cpu_delta = (
                stats["cpu_stats"]["cpu_usage"]["total_usage"]
                - stats["precpu_stats"]["cpu_usage"]["total_usage"]
            )
            system_delta = (
                stats["cpu_stats"]["system_cpu_usage"]
                - stats["precpu_stats"]["system_cpu_usage"]
            )

            if system_delta > 0:
                return (
                    (cpu_delta / system_delta)
                    * len(stats["cpu_stats"]["cpu_usage"]["percpu_usage"])
                    * 100
                )
            return 0.0
        except (KeyError, ZeroDivisionError):
            return 0.0
