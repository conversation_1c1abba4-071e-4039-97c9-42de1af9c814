import re
import sys
from pathlib import Path

#!/usr/bin/env python3
"""
Script to convert SQLAlchemy 2.0 syntax to 1.4 syntax
..""""" ""


def convert_sqlalchemy_models(file_path: str):
    """Convert SQLAlchemy 2.0 syntax to 1.4 syntax..""""" ""

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # Remove Mapped and mapped_column imports
    content = re.sub(
        r"from sqlalchemy\.orm import Mapped, mapped_column, relationship, declarative_base",
        "from sqlalchemy.orm import relationship, declarative_base",
        content,
    )

    # Convert Mapped types to regular Column definitions
    # Pattern: id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    # To: id = Column(Integer, primary_key=True, index=True)
    content = re.sub(
        r"(\w+):\s*Mapped\[[^\]]+\]\s*=\s*mapped_column\(", r"\1 = Column(", content
    )

    # Convert relationship types
    # Pattern: projects: Mapped[List["Project"]] = relationship("Project", back_populates="owner")
    # To: projects = relationship("Project", back_populates="owner")
    content = re.sub(
        r"(\w+):\s*Mapped\[[^\]]+\]\s*=\s*relationship\(",
        r"\1 = relationship(",
        content,
    )

    # Handle any remaining Mapped references
    content = re.sub(r":\s*Mapped\[[^\]]+\]", "", content)

    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)

    print(f"✅ Converted {file_path} to SQLAlchemy 1.4 syntax")


if __name__ == "__main__":
    models_file = "db/models.py"
    if Path(models_file).exists():
        convert_sqlalchemy_models(models_file)
    else:
        print(f"❌ File {models_file} not found")
        sys.exit(1)
