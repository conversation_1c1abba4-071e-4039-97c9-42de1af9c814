# tests/test_phase19_new_routes.py
"""Focused tests for Phase 19 newly added API routes.
These tests mock external systems (Docker, Ollama, monitoring) to validate routing and response shapes.
"""

from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from api.ai_container_routes import router as ai_router
from api.monitoring_dashboard_routes import router as monitoring_router
from api.port_management_routes import router as ports_router
from api.health_check_routes import router as health_router
from api.alert_management_routes import router as alerts_router


@pytest.fixture
def app():
    app = FastAPI()
    app.include_router(ai_router)
    app.include_router(monitoring_router)
    app.include_router(ports_router)
    app.include_router(health_router)
    app.include_router(alerts_router)
    return app


@pytest.fixture
def client(app):
    return TestClient(app)


def test_ai_analyze_site(client):
    with patch("core.ai_container_manager.AIEnhancedContainerManager._check_ollama_availability", return_value=True), \
         patch("core.ai_container_manager.AIEnhancedContainerManager.analyze_and_optimize_site", new_callable=AsyncMock) as mock_analyze:
        mock_analyze.return_value = {
            "success": True,
            "analysis": {"framework": "react"},
            "dockerfile_strategy": "multi-stage",
            "security_hardening": ["pin versions"],
            "performance_optimizations": ["use cache"],
            "estimated_build_time": "fast",
            "recommended_resources": {},
            "ai_confidence": 0.85,
        }
        resp = client.post("/api/ai-containers/analyze/test-site", json={})
        assert resp.status_code == 200
        data = resp.json()
        assert data["success"] is True
        assert data["data"]["dockerfile_strategy"] == "multi-stage"


def test_ports_allocate_and_list(client):
    with patch("api.port_management_routes.SiteContainerManager") as MockMgr:
        mgr = Mock()
        MockMgr.return_value = mgr
        mgr.port_manager.allocate_port.return_value = 8088
        mgr.port_manager.list_allocations.return_value = {"site-a": 8088}

        r1 = client.post("/api/ports/allocate/site-a")
        assert r1.status_code == 200
        r2 = client.get("/api/ports/list")
        assert r2.status_code == 200
        assert r2.json()["allocations"][0]["site_name"] == "site-a"


def test_health_endpoints(client):
    with patch("monitoring.health_check_system.HealthCheckSystem.check_all_containers", new_callable=AsyncMock) as mock_check:
        from types import SimpleNamespace
        from monitoring.health_check_system import HealthStatus
        result = SimpleNamespace(
            container_name="site-site-a",
            container_id="abc123",
            status=HealthStatus.HEALTHY,
            response_time_ms=10.0,
            endpoint="/",
            timestamp=__import__("datetime").datetime.now(),
            error_message=None,
        )
        mock_check.return_value = {"site-a": result}
        r = client.get("/api/health/containers")
        assert r.status_code == 200
        assert r.json()["data"]["summary"]["healthy"] == 1


def test_monitoring_system_status(client):
    with patch("monitoring.alerting_system.AlertingSystem.get_active_alerts", return_value=[]):
        r = client.get("/api/monitoring/system-status")
        assert r.status_code == 200
        assert "overall_status" in r.json()["data"]


def test_alerts_list(client):
    class DummyAlert:
        def __init__(self):
            from datetime import datetime
            self.id = "1"
            self.alert_type = type("T", (), {"value": "high_cpu"})()
            self.severity = type("S", (), {"value": "warning"})()
            self.container_name = "site-x"
            self.message = "High CPU"
            self.timestamp = __import__("datetime").datetime.now()
            self.acknowledged = False
            self.details = {}

    with patch("monitoring.alerting_system.AlertingSystem.get_active_alerts", return_value=[DummyAlert()]):
        r = client.get("/api/monitoring/alerts")
        assert r.status_code == 200
        body = r.json()
        assert body["success"] is True
        assert body["data"]["summary"]["warning"] == 1
