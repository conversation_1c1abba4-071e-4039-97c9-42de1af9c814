# api/enhanced_site_container_routes.py
"""
Enhanced Site Container Management API Routes
Provides REST API endpoints for managing site containers with all requested features.
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from core.site_container_manager import EnvironmentType, SiteContainerManager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/enhanced-sites", tags=["Enhanced Site Containers"])


# Pydantic models for request/response
class SiteConfig(BaseModel):
    """Site configuration model"""

    name: str = Field(..., description="Site name")
    environment: str = Field(default="production", description="Environment type")
    ssl_enabled: bool = Field(default=False, description="Enable SSL")
    backup_enabled: bool = Field(default=True, description="Enable backups")
    monitoring_enabled: bool = Field(default=True, description="Enable monitoring")
    port: Optional[int] = Field(
        None, description="Specific port (auto-assigned if not provided)"
    )


class SSLConfig(BaseModel):
    """SSL configuration model"""

    cert_path: Optional[str] = Field(None, description="SSL certificate path")
    key_path: Optional[str] = Field(None, description="SSL private key path")
    provider: str = Field(
        default="self_signed", description="SSL provider (self_signed, lets_encrypt)"
    )


class ContainerStatus(BaseModel):
    """Container status response model"""

    site_name: str
    status: str
    health_status: str
    port: int
    url: str
    environment: str
    ssl_enabled: bool
    monitoring_enabled: bool
    resource_usage: Optional[Dict[str, Any]]
    last_started: Optional[str]
    last_backup: Optional[str]


class SiteContainer(BaseModel):
    """Site container response model"""

    site_name: str
    port: int
    status: str
    environment: str
    created_at: str
    last_started: Optional[str]
    last_health_check: Optional[str]
    health_status: str
    resource_usage: Optional[Dict[str, Any]]
    ssl_enabled: bool
    ssl_cert_path: Optional[str]
    backup_enabled: bool
    last_backup: Optional[str]
    hot_reload_enabled: bool
    monitoring_enabled: bool


class PortAllocation(BaseModel):
    """Port allocation response model"""

    site_name: str
    port: int
    url: str


class SiteDashboard(BaseModel):
    """Site dashboard response model"""

    summary: Dict[str, Any]
    environments: Dict[str, List[Dict[str, Any]]]
    port_allocations: List[PortAllocation]
    recent_activity: List[Dict[str, Any]]


class ExportRequest(BaseModel):
    """Export request model"""

    target: str = Field(
        default="static", description="Export target (static, vercel, netlify, github)"
    )
    include_assets: bool = Field(default=True, description="Include assets in export")
    optimize: bool = Field(default=True, description="Optimize for production")


# Dependency injection
def get_container_manager() -> SiteContainerManager:
    """Get container manager instance"""
    return SiteContainerManager()


# Health and status endpoints
@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@router.get("/status")
async def get_overall_status(
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """Get overall status of all site containers"""
    try:
        result = await manager.list_containers()
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        containers = result["containers"]
        total = len(containers)
        running = len([c for c in containers if c["status"] == "running"])
        healthy = len([c for c in containers if c["health_status"] == "healthy"])

        return {
            "status": "operational",
            "total_sites": total,
            "running_sites": running,
            "healthy_sites": healthy,
            "uptime_percentage": (healthy / total * 100) if total > 0 else 0,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting overall status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Site container management endpoints
@router.post("/containers")
async def create_site_container(
    config: SiteConfig, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Create a new site container"""
    try:
        site_config = {
            "name": config.name,
            "port": config.port,
            "environment": config.environment,
            "ssl_enabled": config.ssl_enabled,
            "backup_enabled": config.backup_enabled,
            "monitoring_enabled": config.monitoring_enabled,
        }

        result = await manager.create_site_container(config.name, site_config)

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "message": f"Container created for site {config.name}",
            "container": result["container"],
            "port": result["port"],
            "url": result["url"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating site container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/containers/{site_name}/start")
async def start_site_container(
    site_name: str, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Start a site container"""
    try:
        result = await manager.start_site_container(site_name)

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "message": f"Container started for site {site_name}",
            "url": result["url"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting site container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/containers/{site_name}/stop")
async def stop_site_container(
    site_name: str, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Stop a site container"""
    try:
        result = await manager.stop_site_container(site_name)

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        return {"success": True, "message": f"Container stopped for site {site_name}"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping site container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/containers/{site_name}")
async def delete_site_container(
    site_name: str, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Delete a site container"""
    try:
        result = await manager.delete_site_container(site_name)

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        return {"success": True, "message": f"Container deleted for site {site_name}"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting site container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/containers")
async def list_site_containers(
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """List all site containers"""
    try:
        result = await manager.list_containers()

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return {
            "success": True,
            "containers": result["containers"],
            "total": result["total"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing site containers: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/containers/{site_name}")
async def get_site_container_status(
    site_name: str, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Get detailed status of a site container"""
    try:
        result = await manager.get_container_status(site_name)

        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["error"])

        return {"success": True, "container": result["container"]}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting container status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/containers/{site_name}/restart")
async def restart_site_container(
    site_name: str, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Restart a site container"""
    try:
        # Stop container
        stop_result = await manager.stop_site_container(site_name)
        if not stop_result["success"]:
            raise HTTPException(status_code=400, detail=stop_result["error"])

        # Start container
        start_result = await manager.start_site_container(site_name)
        if not start_result["success"]:
            raise HTTPException(status_code=400, detail=start_result["error"])

        return {
            "success": True,
            "message": f"Container restarted for site {site_name}",
            "url": start_result["url"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error restarting site container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/containers/{site_name}/rebuild")
async def rebuild_site_container(
    site_name: str, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Rebuild a site container"""
    try:
        result = await manager.rebuild_site_container(site_name)

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "message": f"Container rebuilt for site {site_name}",
            "url": result.get("url"),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rebuilding site container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Logs and monitoring endpoints
@router.get("/containers/{site_name}/logs")
async def get_container_logs(
    site_name: str,
    lines: int = Query(
        default=100, ge=1, le=1000, description="Number of log lines to retrieve"
    ),
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """Get container logs"""
    try:
        result = await manager.get_container_logs(site_name, lines)

        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["error"])

        return {
            "success": True,
            "logs": result["logs"],
            "total_lines": result["total_lines"],
            "site_name": site_name,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting container logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/monitor/sites")
async def get_sites_monitoring(
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """Get monitoring information for all sites"""
    try:
        result = await manager.list_containers()

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        containers = result["containers"]

        # Calculate monitoring statistics
        monitoring_data = {
            "total_sites": len(containers),
            "running_sites": len([c for c in containers if c["status"] == "running"]),
            "healthy_sites": len(
                [c for c in containers if c["health_status"] == "healthy"]
            ),
            "sites_with_monitoring": len(
                [c for c in containers if c["monitoring_enabled"]]
            ),
            "sites_with_ssl": len([c for c in containers if c["ssl_enabled"]]),
            "resource_usage": {},
            "sites": [],
        }

        # Collect resource usage data
        total_cpu = 0
        total_memory = 0
        sites_with_resources = 0

        for container in containers:
            if container.get("resource_usage"):
                usage = container["resource_usage"]
                total_cpu += usage.get("cpu_percent", 0)
                total_memory += usage.get("memory_usage", 0)
                sites_with_resources += 1

            monitoring_data["sites"].append(
                {
                    "site_name": container["site_name"],
                    "status": container["status"],
                    "health_status": container["health_status"],
                    "port": container["port"],
                    "url": f"http://localhost:{container['port']}",
                    "environment": container["environment"],
                    "ssl_enabled": container["ssl_enabled"],
                    "monitoring_enabled": container["monitoring_enabled"],
                    "resource_usage": container.get("resource_usage"),
                    "last_health_check": container.get("last_health_check"),
                }
            )

        if sites_with_resources > 0:
            monitoring_data["resource_usage"] = {
                "average_cpu_percent": total_cpu / sites_with_resources,
                "total_memory_usage": total_memory,
                "sites_with_resources": sites_with_resources,
            }

        return {
            "success": True,
            "monitoring": monitoring_data,
            "timestamp": datetime.now().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting sites monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Port management endpoints
@router.get("/ports")
async def get_port_allocations(
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """Get current port allocations"""
    try:
        allocations = manager.port_manager.list_allocations()

        formatted_allocations = []
        for site_name, port in allocations.items():
            formatted_allocations.append(
                {
                    "site_name": site_name,
                    "port": port,
                    "url": f"http://localhost:{port}",
                }
            )

        return {
            "success": True,
            "allocations": formatted_allocations,
            "total": len(formatted_allocations),
        }

    except Exception as e:
        logger.error(f"Error getting port allocations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Backup and export endpoints
@router.post("/containers/{site_name}/backup")
async def backup_site_container(
    site_name: str, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Create a backup of a site container"""
    try:
        result = await manager.backup_site_container(site_name)

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "message": f"Backup created for site {site_name}",
            "backup_file": result["backup_file"],
            "timestamp": result["timestamp"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating backup: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/containers/{site_name}/export")
async def export_site(
    site_name: str,
    export_request: ExportRequest,
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """Export site to external hosting"""
    try:
        # This would integrate with the existing ExternalHostingManager
        # For now, we'll create a basic export
        export_dir = Path("exports") / site_name
        export_dir.mkdir(parents=True, exist_ok=True)

        # Copy site files to export directory
        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail=f"Site {site_name} not found")

        import shutil

        shutil.copytree(site_path, export_dir / "site", dirs_exist_ok=True)

        # Create export metadata
        metadata = {
            "site_name": site_name,
            "export_target": export_request.target,
            "exported_at": datetime.now().isoformat(),
            "include_assets": export_request.include_assets,
            "optimize": export_request.optimize,
            "container_info": None,
        }

        # Get container info if available
        if site_name in manager.site_containers:
            container = manager.site_containers[site_name]
            metadata["container_info"] = {
                "port": container.port,
                "environment": container.environment.value,
                "ssl_enabled": container.ssl_enabled,
            }

        import json

        with open(export_dir / "export_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)

        return {
            "success": True,
            "message": f"Site {site_name} exported to {export_request.target}",
            "export_path": str(export_dir),
            "metadata": metadata,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting site: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Development and production environment endpoints
@router.post("/containers/{site_name}/development")
async def setup_development_environment(
    site_name: str, manager: SiteContainerManager = Depends(get_container_manager)
):
    """Set up a complete development environment for a site"""
    try:
        # Create site container with development settings
        site_config = {
            "name": site_name,
            "environment": "development",
            "ssl_enabled": False,
            "backup_enabled": True,
            "monitoring_enabled": True,
        }

        # Create container
        create_result = await manager.create_site_container(site_name, site_config)
        if not create_result["success"]:
            raise HTTPException(status_code=400, detail=create_result["error"])

        # Enable hot reload
        hot_reload_result = await manager.enable_hot_reload(site_name)
        if not hot_reload_result["success"]:
            logger.warning(f"Hot reload setup failed: {hot_reload_result['error']}")

        # Start container
        start_result = await manager.start_site_container(site_name)
        if not start_result["success"]:
            raise HTTPException(status_code=400, detail=start_result["error"])

        return {
            "success": True,
            "message": f"Development environment set up for site {site_name}",
            "url": start_result["url"],
            "features": {
                "hot_reload": hot_reload_result["success"],
                "monitoring": True,
                "backups": True,
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting up development environment: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/containers/{site_name}/production")
async def setup_production_environment(
    site_name: str,
    ssl_config: Optional[SSLConfig] = Body(default=None),
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """Set up a complete production environment for a site"""
    try:
        # Normalize SSL config without constructing a Pydantic model (avoids Pylance arg issues)
        if ssl_config is None:
            _provider = "self_signed"
            _cert_path = None
            _key_path = None
        else:
            _provider = ssl_config.provider
            _cert_path = ssl_config.cert_path
            _key_path = ssl_config.key_path

        # Create site container with production settings
        site_config = {
            "name": site_name,
            "environment": "production",
            "ssl_enabled": _provider != "none",
            "backup_enabled": True,
            "monitoring_enabled": True,
        }

        # Create container
        create_result = await manager.create_site_container(site_name, site_config)
        if not create_result["success"]:
            raise HTTPException(status_code=400, detail=create_result["error"])

        # Configure SSL if requested
        if _provider != "none":
            ssl_result = await manager.configure_ssl(
                site_name,
                {
                    "cert_path": _cert_path,
                    "key_path": _key_path,
                    "provider": _provider,
                },
            )
            if not ssl_result["success"]:
                logger.warning(f"SSL setup failed: {ssl_result['error']}")

        # Start container
        start_result = await manager.start_site_container(site_name)
        if not start_result["success"]:
            raise HTTPException(status_code=400, detail=start_result["error"])

        return {
            "success": True,
            "message": f"Production environment set up for site {site_name}",
            "url": start_result["url"],
            "features": {
                "ssl": _provider != "none",
                "monitoring": True,
                "backups": True,
                "security": True,
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting up production environment: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# SSL configuration endpoint
@router.post("/containers/{site_name}/ssl")
async def configure_ssl(
    site_name: str,
    ssl_config: SSLConfig,
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """Configure SSL for a site container"""
    try:
        result = await manager.configure_ssl(
            site_name,
            {
                "cert_path": ssl_config.cert_path,
                "key_path": ssl_config.key_path,
                "provider": ssl_config.provider,
            },
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        return {"success": True, "message": f"SSL configured for site {site_name}"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error configuring SSL: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Dashboard endpoint
@router.get("/dashboard")
async def get_site_dashboard(
    manager: SiteContainerManager = Depends(get_container_manager),
):
    """Get a comprehensive dashboard of all sites"""
    try:
        # Get all containers
        containers_result = await manager.list_containers()
        if not containers_result["success"]:
            raise HTTPException(status_code=500, detail=containers_result["error"])

        containers = containers_result["containers"]

        # Get port allocations
        allocations = manager.port_manager.list_allocations()
        port_allocations = []
        for site_name, port in allocations.items():
            port_allocations.append(
                {
                    "site_name": site_name,
                    "port": port,
                    "url": f"http://localhost:{port}",
                }
            )

        # Calculate statistics
        total_sites = len(containers)
        running_sites = len([c for c in containers if c["status"] == "running"])
        healthy_sites = len([c for c in containers if c["health_status"] == "healthy"])
        ssl_enabled_sites = len([c for c in containers if c["ssl_enabled"]])

        # Group by environment
        environments = {}
        for container in containers:
            env = container["environment"]
            if env not in environments:
                environments[env] = []
            environments[env].append(container)

        dashboard = {
            "summary": {
                "total_sites": total_sites,
                "running_sites": running_sites,
                "healthy_sites": healthy_sites,
                "ssl_enabled_sites": ssl_enabled_sites,
                "uptime_percentage": (
                    (healthy_sites / total_sites * 100) if total_sites > 0 else 0
                ),
            },
            "environments": environments,
            "port_allocations": port_allocations,
            "recent_activity": [],  # Could be enhanced with activity tracking
        }

        return {
            "success": True,
            "dashboard": dashboard,
            "timestamp": datetime.now().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating site dashboard: {e}")
        raise HTTPException(status_code=500, detail=str(e))
