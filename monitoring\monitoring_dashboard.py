# monitoring/monitoring_dashboard.py
"""
Performance Monitoring Dashboard
Web-based dashboard for real-time container performance tracking and monitoring.
Provides interactive charts, alerts, and comprehensive container insights.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List

from aiohttp import web, web_ws
from aiohttp.web import Application, Request, Response, WebSocketResponse

# aiohttp_cors is optional - gracefully handle if not available
try:
    import aiohttp_cors  # type: ignore
    CORS_AVAILABLE = True
except ImportError:
    CORS_AVAILABLE = False
    aiohttp_cors = None

from monitoring.container_metrics_collector import ContainerMetricsCollector
from monitoring.health_check_system import HealthCheckSystem

logger = logging.getLogger(__name__)


class MonitoringDashboard:
    """
    Web-based monitoring dashboard for container performance tracking.
    Provides real-time metrics, health status, and interactive visualizations.
    """

    def __init__(self, port: int = 8090, host: str = "localhost"):
        """Initialize monitoring dashboard"""
        self.port = port
        self.host = host
        self.app: Application = web.Application()  # Initialize immediately
        self.metrics_collector = ContainerMetricsCollector()
        self.health_system = HealthCheckSystem()
        self.websocket_connections: List[WebSocketResponse] = []
        self.is_running = False

        logger.info(f"Initialized MonitoringDashboard on {host}:{port}")

    async def start_dashboard(self):
        """Start the monitoring dashboard server"""
        if self.is_running:
            logger.warning("Dashboard already running")
            return

        # Start monitoring systems
        await self.metrics_collector.start_collection()
        await self.health_system.start_monitoring()

        # Create web application
        self.app = web.Application()

        # Setup CORS if available
        cors = None
        if CORS_AVAILABLE and aiohttp_cors:
            cors = aiohttp_cors.setup(self.app, defaults={
                "*": aiohttp_cors.ResourceOptions(
                    allow_credentials=True,
                    expose_headers="*",
                    allow_headers="*",
                    allow_methods="*"
                )
            })

        # Setup routes
        self._setup_routes()

        # Add CORS to all routes if available
        if cors:
            for route in list(self.app.router.routes()):
                cors.add(route)

        # Start background tasks
        asyncio.create_task(self._websocket_broadcaster())

        # Start web server
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)
        await site.start()

        self.is_running = True
        logger.info(f"🚀 Monitoring Dashboard started at http://{self.host}:{self.port}")

    async def stop_dashboard(self):
        """Stop the monitoring dashboard"""
        if not self.is_running:
            return

        # Stop monitoring systems
        await self.metrics_collector.stop_collection()
        await self.health_system.stop_monitoring()

        # Close websocket connections
        for ws in self.websocket_connections:
            if not ws.closed:
                await ws.close()

        self.is_running = False
        logger.info("Stopped monitoring dashboard")

    def _setup_routes(self):
        """Setup web application routes"""
        # Static files
        self.app.router.add_get('/', self._serve_dashboard)
        self.app.router.add_get('/dashboard', self._serve_dashboard)

        # API endpoints
        self.app.router.add_get('/api/metrics', self._api_get_metrics)
        self.app.router.add_get('/api/health', self._api_get_health)
        self.app.router.add_get('/api/summary', self._api_get_summary)
        self.app.router.add_get('/api/container/{name}/metrics', self._api_get_container_metrics)
        self.app.router.add_get('/api/container/{name}/health', self._api_get_container_health)

        # WebSocket endpoint
        self.app.router.add_get('/ws', self._websocket_handler)

    async def _serve_dashboard(self, request: Request) -> Response:
        """Serve the main dashboard HTML"""
        logger.debug(f"Serving dashboard to {request.remote}")
        html_content = self._generate_dashboard_html()
        return web.Response(text=html_content, content_type='text/html')

    async def _api_get_metrics(self, request: Request) -> Response:
        """API endpoint for current metrics"""
        try:
            logger.debug(f"API metrics request from {request.remote}")
            metrics = self.metrics_collector.get_all_current_metrics()

            # Convert to JSON-serializable format
            data = {}
            for container_name, metric in metrics.items():
                data[container_name] = {
                    "container_id": metric.container_id,
                    "timestamp": metric.timestamp.isoformat(),
                    "cpu_usage_percent": metric.cpu_usage_percent,
                    "memory_usage_percent": metric.memory_usage_percent,
                    "memory_usage_mb": metric.memory_usage_mb,
                    "memory_limit_mb": metric.memory_limit_mb,
                    "network_rx_mb": metric.network_rx_bytes / (1024 * 1024),
                    "network_tx_mb": metric.network_tx_bytes / (1024 * 1024),
                    "status": metric.status,
                    "health_status": metric.health_status,
                    "uptime_hours": metric.uptime_seconds / 3600,
                    "restart_count": metric.restart_count
                }

            return web.json_response(data)

        except Exception as e:
            logger.error(f"Error getting metrics: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def _api_get_health(self, request: Request) -> Response:
        """API endpoint for health status"""
        try:
            logger.debug(f"API health request from {request.remote}")
            health_status = self.health_system.get_all_health_status()
            return web.json_response(health_status)

        except Exception as e:
            logger.error(f"Error getting health status: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def _api_get_summary(self, request: Request) -> Response:
        """API endpoint for dashboard summary"""
        try:
            logger.debug(f"API summary request from {request.remote}")
            metrics = self.metrics_collector.get_all_current_metrics()
            health_status = self.health_system.get_all_health_status()

            summary = {
                "total_containers": len(metrics),
                "healthy_containers": sum(1 for h in health_status.values() if h.get("status") == "healthy"),
                "unhealthy_containers": sum(1 for h in health_status.values() if h.get("status") == "unhealthy"),
                "total_cpu_usage": sum(m.cpu_usage_percent for m in metrics.values()),
                "total_memory_usage_mb": sum(m.memory_usage_mb for m in metrics.values()),
                "average_cpu_usage": sum(m.cpu_usage_percent for m in metrics.values()) / len(metrics) if metrics else 0,
                "average_memory_usage": sum(m.memory_usage_percent for m in metrics.values()) / len(metrics) if metrics else 0,
                "timestamp": datetime.now().isoformat()
            }

            return web.json_response(summary)

        except Exception as e:
            logger.error(f"Error getting summary: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def _api_get_container_metrics(self, request: Request) -> Response:
        """API endpoint for specific container metrics"""
        try:
            container_name = request.match_info['name']
            minutes = int(request.query.get('minutes', 60))

            summary = self.metrics_collector.get_performance_summary(container_name, minutes)
            return web.json_response(summary)

        except Exception as e:
            logger.error(f"Error getting container metrics: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def _api_get_container_health(self, request: Request) -> Response:
        """API endpoint for specific container health"""
        try:
            container_name = request.match_info['name']
            minutes = int(request.query.get('minutes', 60))

            summary = self.health_system.get_health_summary(container_name, minutes)
            return web.json_response(summary)

        except Exception as e:
            logger.error(f"Error getting container health: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def _websocket_handler(self, request: Request) -> WebSocketResponse:
        """WebSocket handler for real-time updates"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websocket_connections.append(ws)
        logger.info("New WebSocket connection established")

        try:
            async for msg in ws:
                if msg.type == web_ws.WSMsgType.TEXT:
                    # Handle incoming messages if needed
                    pass
                elif msg.type == web_ws.WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            if ws in self.websocket_connections:
                self.websocket_connections.remove(ws)
            logger.info("WebSocket connection closed")

        return ws

    async def _websocket_broadcaster(self):
        """Broadcast real-time updates to WebSocket connections"""
        while self.is_running:
            try:
                if self.websocket_connections:
                    # Get current data
                    metrics = self.metrics_collector.get_all_current_metrics()
                    health_status = self.health_system.get_all_health_status()

                    # Prepare update message
                    update = {
                        "type": "update",
                        "timestamp": datetime.now().isoformat(),
                        "metrics": {},
                        "health": health_status
                    }

                    # Convert metrics to JSON-serializable format
                    for container_name, metric in metrics.items():
                        update["metrics"][container_name] = {
                            "cpu_usage_percent": metric.cpu_usage_percent,
                            "memory_usage_percent": metric.memory_usage_percent,
                            "memory_usage_mb": metric.memory_usage_mb,
                            "status": metric.status,
                            "health_status": metric.health_status
                        }

                    # Send to all connected clients
                    message = json.dumps(update)
                    disconnected = []

                    for ws in self.websocket_connections:
                        try:
                            if not ws.closed:
                                await ws.send_str(message)
                            else:
                                disconnected.append(ws)
                        except Exception as e:
                            logger.error(f"Error sending WebSocket message: {e}")
                            disconnected.append(ws)

                    # Remove disconnected clients
                    for ws in disconnected:
                        if ws in self.websocket_connections:
                            self.websocket_connections.remove(ws)

                await asyncio.sleep(5)  # Update every 5 seconds

            except Exception as e:
                logger.error(f"Error in WebSocket broadcaster: {e}")
                await asyncio.sleep(5)

    def _generate_dashboard_html(self) -> str:
        """Generate the dashboard HTML page"""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Container Monitoring Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .summary-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .summary-card h3 { margin: 0 0 10px 0; color: #2c3e50; }
        .summary-card .value { font-size: 2em; font-weight: bold; color: #3498db; }
        .containers { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .container-card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; }
        .container-header { background: #34495e; color: white; padding: 15px; }
        .container-body { padding: 20px; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .metric-label { font-weight: bold; }
        .status-healthy { color: #27ae60; }
        .status-unhealthy { color: #e74c3c; }
        .status-unknown { color: #f39c12; }
        .progress-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; margin-top: 5px; }
        .progress-fill { height: 100%; background: #3498db; transition: width 0.3s ease; }
        .progress-fill.high { background: #e74c3c; }
        .progress-fill.medium { background: #f39c12; }
        .refresh-indicator { position: fixed; top: 20px; right: 20px; background: #27ae60; color: white; padding: 10px; border-radius: 4px; display: none; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🐳 Container Monitoring Dashboard</h1>
        <p>Real-time monitoring of Docker containers</p>
    </div>

    <div class="refresh-indicator" id="refreshIndicator">🔄 Updating...</div>

    <div class="summary" id="summary">
        <!-- Summary cards will be populated by JavaScript -->
    </div>

    <div class="containers" id="containers">
        <!-- Container cards will be populated by JavaScript -->
    </div>

    <script>
        let ws = null;

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws`);

            ws.onopen = function() {
                console.log('WebSocket connected');
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'update') {
                    updateDashboard(data);
                    showRefreshIndicator();
                }
            };

            ws.onclose = function() {
                console.log('WebSocket disconnected, reconnecting...');
                setTimeout(connectWebSocket, 5000);
            };

            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }

        function showRefreshIndicator() {
            const indicator = document.getElementById('refreshIndicator');
            indicator.style.display = 'block';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 1000);
        }

        function updateDashboard(data) {
            updateSummary(data);
            updateContainers(data);
        }

        function updateSummary(data) {
            const metrics = data.metrics;
            const health = data.health;

            const totalContainers = Object.keys(metrics).length;
            const healthyContainers = Object.values(health).filter(h => h.status === 'healthy').length;
            const avgCpu = totalContainers > 0 ? Object.values(metrics).reduce((sum, m) => sum + m.cpu_usage_percent, 0) / totalContainers : 0;
            const avgMemory = totalContainers > 0 ? Object.values(metrics).reduce((sum, m) => sum + m.memory_usage_percent, 0) / totalContainers : 0;

            document.getElementById('summary').innerHTML = `
                <div class="summary-card">
                    <h3>Total Containers</h3>
                    <div class="value">${totalContainers}</div>
                </div>
                <div class="summary-card">
                    <h3>Healthy Containers</h3>
                    <div class="value status-healthy">${healthyContainers}</div>
                </div>
                <div class="summary-card">
                    <h3>Average CPU</h3>
                    <div class="value">${avgCpu.toFixed(1)}%</div>
                </div>
                <div class="summary-card">
                    <h3>Average Memory</h3>
                    <div class="value">${avgMemory.toFixed(1)}%</div>
                </div>
            `;
        }

        function updateContainers(data) {
            const containers = document.getElementById('containers');
            containers.innerHTML = '';

            for (const [name, metrics] of Object.entries(data.metrics)) {
                const health = data.health[name] || {};
                const healthStatus = health.status || 'unknown';
                const healthClass = `status-${healthStatus}`;

                const cpuClass = metrics.cpu_usage_percent > 80 ? 'high' : metrics.cpu_usage_percent > 50 ? 'medium' : '';
                const memoryClass = metrics.memory_usage_percent > 80 ? 'high' : metrics.memory_usage_percent > 50 ? 'medium' : '';

                const containerCard = document.createElement('div');
                containerCard.className = 'container-card';
                containerCard.innerHTML = `
                    <div class="container-header">
                        <h3>${name}</h3>
                        <span class="${healthClass}">● ${healthStatus.toUpperCase()}</span>
                    </div>
                    <div class="container-body">
                        <div class="metric">
                            <span class="metric-label">CPU Usage:</span>
                            <span>${metrics.cpu_usage_percent.toFixed(1)}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill ${cpuClass}" style="width: ${metrics.cpu_usage_percent}%"></div>
                        </div>

                        <div class="metric">
                            <span class="metric-label">Memory Usage:</span>
                            <span>${metrics.memory_usage_percent.toFixed(1)}% (${metrics.memory_usage_mb.toFixed(0)} MB)</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill ${memoryClass}" style="width: ${metrics.memory_usage_percent}%"></div>
                        </div>

                        <div class="metric">
                            <span class="metric-label">Status:</span>
                            <span>${metrics.status}</span>
                        </div>

                        <div class="metric">
                            <span class="metric-label">Health Status:</span>
                            <span class="${healthClass}">${metrics.health_status}</span>
                        </div>
                    </div>
                `;

                containers.appendChild(containerCard);
            }
        }

        // Initial load
        async function loadInitialData() {
            try {
                const [metricsResponse, healthResponse] = await Promise.all([
                    fetch('/api/metrics'),
                    fetch('/api/health')
                ]);

                const metrics = await metricsResponse.json();
                const health = await healthResponse.json();

                updateDashboard({ metrics, health });
            } catch (error) {
                console.error('Error loading initial data:', error);
            }
        }

        // Start everything
        loadInitialData();
        connectWebSocket();
    </script>
</body>
</html>
        """
