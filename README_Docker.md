# 🐳 Docker Setup for AI Coding Agent

This document provides comprehensive instructions for running the AI Coding Agent using Docker Desktop with enhanced containerization, monitoring, and AI-powered optimizations.

## 🚀 **New Features in Phase 19**

### ✅ **Enhanced Container Management**
- **🤖 AI-Powered Optimization**: Intelligent container creation with local Ollama models
- **📊 Real-time Monitoring**: Comprehensive metrics collection and health monitoring
- **🚨 Smart Alerting**: Automated alerts for performance issues and failures
- **🌐 Web Dashboard**: Interactive monitoring dashboard with live updates
- **🔧 Dynamic Port Management**: Automatic port allocation and conflict resolution

### ✅ **Advanced Docker Features**
- **🔒 Security-First**: Pinned base images, non-root users, vulnerability scanning
- **⚡ Performance Optimized**: Multi-stage builds, intelligent caching, resource limits
- **📁 Smart File Management**: Framework-specific .dockerignore generation
- **🔄 Environment Management**: Site-specific environment files with centralized configuration

### ✅ **Monitoring & Observability**
- **📈 Container Metrics**: CPU, memory, network, and disk usage tracking
- **🏥 Health Checks**: Automated health monitoring with configurable endpoints
- **📊 Performance Dashboard**: Real-time web dashboard at `http://localhost:8090`
- **🚨 Intelligent Alerts**: Rule-based alerting with email notifications
- **📋 Historical Data**: Metrics history with trend analysis and cleanup

## 📋 Prerequisites

### 1. Install Docker Desktop
- **Windows**: Download from [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
- **macOS**: Download from [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop)
- **Linux**: Follow [Docker Engine installation](https://docs.docker.com/engine/install/)

### 2. System Requirements
- **RAM**: Minimum 8GB, Recommended 16GB+
- **Storage**: At least 20GB free space
- **CPU**: Multi-core processor recommended
- **GPU**: Optional, for fine-tuning with CUDA support
- **Network**: Docker network `ai-coding-network` (auto-created)

### 3. Virtual Environment Setup
```bash
# Windows PowerShell
.\.venv\Scripts\Activate.ps1

# Unix/macOS
source .venv/bin/activate
```

## 🚀 Quick Start

### 1. Clone and Navigate
```bash
git clone <repository-url>
cd AICodingAgent
```

### 2. Build and Start
```bash
# Build all Docker images
python scripts/docker_management.py build

# Start all services
python scripts/docker_management.py start
```

### 3. Access the Application
- **Main API**: http://localhost:8000
- **Dashboard**: http://localhost:8001
- **Frontend**: http://localhost:3000
- **Health Check**: http://localhost:8000/health

## 🏗️ Architecture

### Services Overview

| Service | Port | Description |
|---------|------|-------------|
| `ai-coding-agent` | 8000 | Main application API |
| `redis` | 6379 | Caching and session management |
| `postgres` | 5432 | Database persistence |
| `monitoring` | - | System monitoring |
| `recovery` | - | Recovery system |
| `validation` | - | Validation system |
| `fine-tuning` | - | AI model fine-tuning |
| `nginx` | 80/443 | Reverse proxy |
| `backup` | - | Automated backups |
| `security` | - | Security scanning |

### Network Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │   Main Agent    │    │   PostgreSQL    │
│   (80/443)      │◄──►│   (8000)        │◄──►│   (5432)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌────────┴────────┐              │
         │              │                 │              │
         │        ┌─────▼─────┐    ┌─────▼─────┐         │
         │        │   Redis   │    │ Monitoring│         │
         │        │  (6379)   │    │           │         │
         │        └───────────┘    └───────────┘         │
         │                                                   │
    ┌────▼────┐    ┌──────────┐    ┌──────────┐    ┌────────▼────────┐
    │ Recovery│    │Validation│    │Fine-tune │    │   Backup        │
    │         │    │          │    │          │    │   Security      │
    └─────────┘    └──────────┘    └──────────┘    └─────────────────┘
```

## 📁 Volume Mounts

### Persistent Data
```yaml
volumes:
  - ./data:/app/data              # Application data
  - ./logs:/app/logs              # Log files
  - ./backups:/app/backups        # Backup files
  - ./sites:/app/sites            # Generated websites
  - ./uploads:/app/uploads        # User uploads
  - ./ssl:/app/ssl                # SSL certificates
  - ./database:/app/database      # Database files
  - ./config:/app/config          # Configuration files
  - ./test_reports:/app/test_reports  # Test reports
```

### Docker Volumes
```yaml
volumes:
  redis_data:      # Redis persistence
  postgres_data:   # PostgreSQL data
```

## 🛠️ Management Commands

### Using the Management Script
```bash
# Build images
python scripts/docker_management.py build

# Start services
python scripts/docker_management.py start

# Stop services
python scripts/docker_management.py stop

# Restart services
python scripts/docker_management.py restart

# Check status
python scripts/docker_management.py status

# View logs
python scripts/docker_management.py logs
python scripts/docker_management.py logs ai-coding-agent

# Scale services
python scripts/docker_management.py scale ai-coding-agent 3

# Health check
python scripts/docker_management.py health

# Backup volumes
python scripts/docker_management.py backup

# Restore volumes
python scripts/docker_management.py restore 20250804_141525

# Cleanup
python scripts/docker_management.py cleanup
```

### Using Docker Compose Directly
```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild and restart
docker-compose up -d --build

# Scale services
docker-compose up -d --scale ai-coding-agent=3
```

## 🔧 Configuration

### Environment Variables
```bash
# Main application
PYTHONPATH=/app
ENVIRONMENT=production
LOG_LEVEL=INFO

# Database
POSTGRES_DB=ai_coding_agent
POSTGRES_USER=ai_coding_user
POSTGRES_PASSWORD=ai_coding_password

# Services
MONITORING_ENABLED=true
RECOVERY_ENABLED=true
VALIDATION_ENABLED=true
FINE_TUNING_ENABLED=true
BACKUP_ENABLED=true
SECURITY_ENABLED=true
```

### Custom Configuration
1. Create `.env` file in project root
2. Override default environment variables
3. Restart services: `docker-compose restart`

## 🔍 Monitoring and Debugging

### Health Checks
```bash
# Check all services
docker-compose ps

# Check specific service
docker-compose ps ai-coding-agent

# Health check endpoint
curl http://localhost:8000/health
```

### Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs ai-coding-agent

# Follow logs
docker-compose logs -f

# Last 100 lines
docker-compose logs --tail=100
```

### Resource Usage
```bash
# Container stats
docker stats

# Disk usage
docker system df

# Resource usage details
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
```

## 🔒 Security

### SSL/TLS Setup
1. Place SSL certificates in `./ssl/`
2. Update nginx configuration
3. Restart nginx service

### Security Scanning
```bash
# Run security scan
docker-compose exec security python -m security.automated_security_monitor

# View security logs
docker-compose logs security
```

### Access Control
- Services communicate via internal Docker network
- External access only through nginx proxy
- Database not exposed externally
- Redis not exposed externally

## 💾 Backup and Recovery

### Automated Backups
```bash
# Create backup
python scripts/docker_management.py backup

# List backups
ls -la backups/docker/

# Restore backup
python scripts/docker_management.py restore 20250804_141525
```

### Manual Backups
```bash
# Backup volumes
docker run --rm -v ai-coding-agent_postgres_data:/data -v $(pwd)/backups:/backup alpine tar czf /backup/postgres_backup_$(date +%Y%m%d_%H%M%S).tar.gz -C /data .

# Backup application data
tar czf backups/app_data_$(date +%Y%m%d_%H%M%S).tar.gz data/ logs/ sites/ uploads/
```

## 🚀 Performance Optimization

### Resource Limits
```yaml
services:
  ai-coding-agent:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

### GPU Support
```yaml
services:
  fine-tuning:
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

### Scaling
```bash
# Scale main service
docker-compose up -d --scale ai-coding-agent=3

# Scale monitoring
docker-compose up -d --scale monitoring=2
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Port Conflicts
```bash
# Check port usage
netstat -tulpn | grep :8000

# Change ports in docker-compose.yml
ports:
  - "8001:8000"  # Use different host port
```

#### 2. Permission Issues
```bash
# Fix volume permissions
sudo chown -R $USER:$USER data/ logs/ backups/

# Or run with proper user
docker-compose run --user $(id -u):$(id -g) ai-coding-agent
```

#### 3. Memory Issues
```bash
# Increase Docker memory limit
# Docker Desktop → Settings → Resources → Memory

# Check memory usage
docker stats
```

#### 4. Build Failures
```bash
# Clean build
docker-compose build --no-cache

# Remove old images
docker image prune -f

# Check build context
docker build --progress=plain .
```

### Debug Mode
```bash
# Run with debug logging
docker-compose up -d --env-file .env.debug

# Attach to running container
docker-compose exec ai-coding-agent bash

# Run commands in container
docker-compose exec ai-coding-agent python -c "import sys; print(sys.path)"
```

## 📊 Monitoring Dashboard

### Access Monitoring
- **Grafana**: http://localhost:3000 (if configured)
- **Prometheus**: http://localhost:9090 (if configured)
- **Application Metrics**: http://localhost:8000/api/v1/robustness/monitoring/dashboard

### Key Metrics
- CPU and Memory usage
- Request rates and response times
- Error rates and logs
- Database performance
- Network activity

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Docker Build and Deploy

on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Build Docker images
        run: docker-compose build
      - name: Run tests
        run: docker-compose run ai-coding-agent python -m pytest
      - name: Deploy
        run: docker-compose up -d
```

## 📚 Additional Resources

### Documentation
- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [AI Coding Agent Documentation](./docs/)

### Support
- Check logs: `docker-compose logs`
- Health check: `curl http://localhost:8000/health`
- GitHub Issues: [Report Issues](https://github.com/your-repo/issues)

### Performance Tips
1. Use `.dockerignore` to reduce build context
2. Leverage Docker layer caching
3. Use multi-stage builds for production
4. Monitor resource usage regularly
5. Implement proper logging and monitoring

---

**🎉 You're now ready to run the AI Coding Agent with Docker!**
