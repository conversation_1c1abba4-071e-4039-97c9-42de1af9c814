# api/monitoring_dashboard_routes.py
"""
Monitoring Dashboard API Routes
Provides REST API endpoints for comprehensive container monitoring and dashboard data.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from api.agent_dependency import get_agent
from core.container_monitor import ContainerMonitor
from monitoring.container_metrics_collector import ContainerMetricsCollector
from monitoring.health_check_system import HealthCheckSystem
from monitoring.alerting_system import AlertingSystem

router = APIRouter(prefix="/api/monitoring", tags=["Monitoring Dashboard"])


class MonitoringResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str
    error: Optional[str] = None


class DashboardData(BaseModel):
    containers: Dict[str, Any]
    system_metrics: Dict[str, Any]
    health_status: Dict[str, Any]
    alerts: List[Dict[str, Any]]
    summary: Dict[str, Any]


@router.get("/dashboard/data", response_model=MonitoringResponse)
async def get_dashboard_data(agent=Depends(get_agent)):
    """Get comprehensive dashboard data for monitoring interface"""
    try:
        # Initialize monitoring components
        container_monitor = ContainerMonitor()
        metrics_collector = ContainerMetricsCollector()
        health_system = HealthCheckSystem()
        alerting_system = AlertingSystem()

        # Collect all monitoring data
        containers_result = await container_monitor.get_container_status()
        containers_list = containers_result.get("containers", []) if isinstance(containers_result, dict) else []
        metrics_data = await metrics_collector.collect_all_metrics()
        health_data = await health_system.check_all_containers()
        alerts_data = alerting_system.get_active_alerts()

        # Calculate summary statistics
        total_containers = len(containers_list)

        def _health_value(h):
            try:
                return h.value if hasattr(h, "value") else h
            except Exception:
                return h

        healthy_containers = sum(1 for c in containers_list if _health_value(c.get("health")) == "healthy")
        unhealthy_containers = total_containers - healthy_containers

        # System resource summary
        total_cpu = sum(m.cpu_usage_percent for m in metrics_data.values()) if metrics_data else 0
        total_memory = sum(m.memory_usage_mb for m in metrics_data.values()) if metrics_data else 0
        avg_cpu = total_cpu / total_containers if total_containers > 0 else 0
        avg_memory = (sum(m.memory_usage_percent for m in metrics_data.values()) / total_containers) if (total_containers > 0 and metrics_data) else 0

        def _iso(ts):
            try:
                return ts.isoformat()
            except Exception:
                return str(ts)

        dashboard_data = {
            "containers": {
                c.get("name"): {
                    "name": c.get("name"),
                    "status": c.get("status"),
                    "health": _health_value(c.get("health")),
                    "uptime": c.get("uptime"),
                    "restart_count": c.get("restart_count"),
                    "memory_usage": c.get("memory_usage"),
                    "cpu_usage": c.get("cpu_usage"),
                    "port_mappings": c.get("port_mappings"),
                    "last_check": _iso(c.get("last_check")),
                }
                for c in containers_list
            },
            "system_metrics": {
                "total_cpu_usage": total_cpu,
                "total_memory_usage_mb": total_memory,
                "average_cpu_usage": avg_cpu,
                "average_memory_usage": avg_memory,
                "metrics_by_container": {
                    name: {
                        "cpu_percent": metric.cpu_usage_percent,
                        "memory_percent": metric.memory_usage_percent,
                        "memory_mb": metric.memory_usage_mb,
                        "network_rx_mb": metric.network_rx_bytes / (1024 * 1024),
                        "network_tx_mb": metric.network_tx_bytes / (1024 * 1024),
                        "disk_io_read_mb": getattr(metric, "disk_io_read_bytes", getattr(metric, "disk_read_bytes", 0)) / (1024 * 1024),
                        "disk_io_write_mb": getattr(metric, "disk_io_write_bytes", getattr(metric, "disk_write_bytes", 0)) / (1024 * 1024)
                    }
                    for name, metric in metrics_data.items()
                }
            },
            "health_status": {
                name: {
                    "status": result.status.value,
                    "response_time_ms": result.response_time_ms,
                    "endpoint": result.endpoint,
                    "last_check": result.timestamp.isoformat(),
                    "error_message": result.error_message
                }
                for name, result in health_data.items()
            },
            "alerts": [
                {
                    "id": alert.id,
                    "type": getattr(alert.alert_type, "value", alert.alert_type),
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "container_name": alert.container_name,
                    "timestamp": alert.timestamp.isoformat(),
                    "acknowledged": getattr(alert, "acknowledged", False)
                }
                for alert in alerts_data
            ],
            "summary": {
                "total_containers": total_containers,
                "healthy_containers": healthy_containers,
                "unhealthy_containers": unhealthy_containers,
                "active_alerts": len(alerts_data),
                "critical_alerts": len([a for a in alerts_data if a.severity.value == "critical"]),
                "warning_alerts": len([a for a in alerts_data if a.severity.value == "warning"]),
                "system_health": "healthy" if unhealthy_containers == 0 and len([a for a in alerts_data if a.severity.value == "critical"]) == 0 else "degraded"
            }
        }

        return MonitoringResponse(
            success=True,
            message="Dashboard data retrieved successfully",
            data=dashboard_data,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/containers/{container_name}/metrics", response_model=MonitoringResponse)
async def get_container_metrics(
    container_name: str,
    minutes: int = Query(60, description="Number of minutes of history to retrieve"),
    agent=Depends(get_agent)
):
    """Get detailed metrics for a specific container"""
    try:
        metrics_collector = ContainerMetricsCollector()

        # Get performance summary for the specified time period
        summary = metrics_collector.get_performance_summary(container_name, minutes)

        if not summary:
            raise HTTPException(status_code=404, detail=f"Container {container_name} not found or no metrics available")

        return MonitoringResponse(
            success=True,
            message=f"Metrics retrieved for container {container_name}",
            data=summary,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health-checks", response_model=MonitoringResponse)
async def get_health_checks(agent=Depends(get_agent)):
    """Get health check status for all containers"""
    try:
        health_system = HealthCheckSystem()

        # Get health status for all configured containers
        health_results = await health_system.check_all_containers()

        health_data = {
            "containers": {
                name: {
                    "container_name": result.container_name,
                    "status": result.status.value,
                    "response_time_ms": result.response_time_ms,
                    "endpoint": result.endpoint,
                    "last_check": result.timestamp.isoformat(),
                    "error_message": result.error_message,
                    "container_id": result.container_id
                }
                for name, result in health_results.items()
            },
            "summary": {
                "total_containers": len(health_results),
                "healthy": len([r for r in health_results.values() if r.status.value == "healthy"]),
                "unhealthy": len([r for r in health_results.values() if r.status.value == "unhealthy"]),
                "unknown": len([r for r in health_results.values() if r.status.value == "unknown"])
            }
        }

        return MonitoringResponse(
            success=True,
            message="Health check status retrieved successfully",
            data=health_data,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system-status", response_model=MonitoringResponse)
async def get_system_status(agent=Depends(get_agent)):
    """Get overall system status and health"""
    try:
        # Initialize monitoring components
        container_monitor = ContainerMonitor()
        metrics_collector = ContainerMetricsCollector()
        health_system = HealthCheckSystem()
        alerting_system = AlertingSystem()

        # Get monitoring status from each component
        monitoring_status = {
            "container_monitor": {
                "active": hasattr(container_monitor, 'is_monitoring') and container_monitor.is_monitoring,
                "monitored_containers": len(container_monitor.monitored_containers) if hasattr(container_monitor, 'monitored_containers') else 0
            },
            "metrics_collector": {
                "active": metrics_collector.is_collecting if hasattr(metrics_collector, 'is_collecting') else True,
                "collection_interval": getattr(metrics_collector, 'collection_interval', 30),
                "containers_tracked": len(metrics_collector.metrics_history) if hasattr(metrics_collector, 'metrics_history') else 0
            },
            "health_system": {
                "active": health_system.is_monitoring if hasattr(health_system, 'is_monitoring') else True,
                "check_interval": getattr(health_system, 'check_interval', 30),
                "containers_configured": len(health_system.health_configs) if hasattr(health_system, 'health_configs') else 0
            },
            "alerting_system": {
                "active": alerting_system.is_monitoring if hasattr(alerting_system, 'is_monitoring') else True,
                "active_alerts": len(alerting_system.get_active_alerts()),
                "alert_rules": len(getattr(alerting_system, 'alert_rules', []))
            }
        }

        # Determine overall system health
        all_systems_active = all(
            status.get("active", False) for status in monitoring_status.values()
        )

        active_critical_alerts = len([
            alert for alert in alerting_system.get_active_alerts()
            if alert.severity.value == "critical"
        ])

        if active_critical_alerts > 0:
            overall_status = "critical"
        elif not all_systems_active:
            overall_status = "degraded"
        else:
            overall_status = "healthy"

        system_data = {
            "overall_status": overall_status,
            "monitoring_components": monitoring_status,
            "system_info": {
                "uptime": "N/A",  # Could be implemented with system uptime
                "version": "1.0.0",
                "environment": "production"
            },
            "alerts_summary": {
                "total_active": len(alerting_system.get_active_alerts()),
                "critical": active_critical_alerts,
                "warning": len([
                    alert for alert in alerting_system.get_active_alerts()
                    if alert.severity.value == "warning"
                ])
            }
        }

        return MonitoringResponse(
            success=True,
            message="System status retrieved successfully",
            data=system_data,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts", response_model=MonitoringResponse)
async def get_active_alerts(
    severity: Optional[str] = Query(None, description="Filter by severity: critical, warning, info"),
    container_name: Optional[str] = Query(None, description="Filter by container name"),
    agent=Depends(get_agent)
):
    """Get active alerts with optional filtering"""
    try:
        alerting_system = AlertingSystem()

        # Get all active alerts
        alerts = alerting_system.get_active_alerts()

        # Apply filters
        if severity:
            alerts = [alert for alert in alerts if alert.severity.value == severity.lower()]

        if container_name:
            alerts = [alert for alert in alerts if alert.container_name == container_name]

        alerts_data = {
            "alerts": [
                {
                    "id": alert.id,
                    "type": getattr(alert.alert_type, "value", alert.alert_type),
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "container_name": alert.container_name,
                    "timestamp": alert.timestamp.isoformat(),
                    "acknowledged": getattr(alert, "acknowledged", False),
                    "details": getattr(alert, 'details', {})
                }
                for alert in alerts
            ],
            "summary": {
                "total": len(alerts),
                "critical": len([a for a in alerts if a.severity.value == "critical"]),
                "warning": len([a for a in alerts if a.severity.value == "warning"]),
                "info": len([a for a in alerts if a.severity.value == "info"])
            }
        }

        return MonitoringResponse(
            success=True,
            message=f"Retrieved {len(alerts)} active alerts",
            data=alerts_data,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
