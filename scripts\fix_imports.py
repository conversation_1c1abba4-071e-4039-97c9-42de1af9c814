import os
import re
from pathlib import Path
from typing import List, Tuple

#!/usr/bin/env python3
"""
<PERSON>ript to convert relative imports to absolute imports across the project.
..""""" ""


def find_python_files(root_dir: str = ".") -> List[Path]:
    """Find all Python files in the project...""""" ""
    python_files = []
    for root, dirs, files in os.walk(root_dir):
        # Skip virtual environments and cache directories
        dirs[:] = [
            d for d in dirs if d not in {".venv", "__pycache__", ".git", "node_modules"}
        ]

        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    return python_files


def convert_relative_to_absolute_import(file_path: Path) -> List[str]:
    """Convert relative imports to absolute imports in a file...""""" ""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content
        changes = []

        # Convert relative imports to absolute
        # Pattern for from . import
        pattern1 = r"from \.([a-zA-Z_][a-zA-Z0-9_]*) import"
        matches1 = re.finditer(pattern1, content)
        for match in matches1:
            module_name = match.group(1)
            # Get the relative path from project root
            rel_path = file_path.relative_to(Path("."))
            parts = list(rel_path.parent.parts)
            if parts and parts[0] != ".":
                absolute_path = ".".join(parts + [module_name])
                old_import = match.group(0)
                new_import = f"from {absolute_path} import"
                content = content.replace(old_import, new_import)
                changes.append(f"  {old_import} -> {new_import}")

        # Pattern for from .. import
        pattern2 = r"from \.\.([a-zA-Z_][a-zA-Z0-9_]*) import"
        matches2 = re.finditer(pattern2, content)
        for match in matches2:
            module_name = match.group(1)
            # Get the relative path from project root
            rel_path = file_path.relative_to(Path("."))
            parts = list(rel_path.parent.parts)
            if len(parts) > 1:
                parts = parts[:-1]  # Remove one level
                absolute_path = ".".join(parts + [module_name])
                old_import = match.group(0)
                new_import = f"from {absolute_path} import"
                content = content.replace(old_import, new_import)
                changes.append(f"  {old_import} -> {new_import}")

        # Pattern for from scripts.module import
        pattern3 = r"from \.([a-zA-Z_][a-zA-Z0-9_]*)\.[a-zA-Z_][a-zA-Z0-9_]* import"
        matches3 = re.finditer(pattern3, content)
        for match in matches3:
            module_name = match.group(1)
            # Get the relative path from project root
            rel_path = file_path.relative_to(Path("."))
            parts = list(rel_path.parent.parts)
            if parts and parts[0] != ".":
                absolute_path = ".".join(parts + [module_name])
                old_import = match.group(0)
                new_import = old_import.replace(f".{module_name}", absolute_path)
                content = content.replace(old_import, new_import)
                changes.append(f"  {old_import} -> {new_import}")

        # Pattern for import .
        pattern4 = r"import \.([a-zA-Z_][a-zA-Z0-9_]*)"
        matches4 = re.finditer(pattern4, content)
        for match in matches4:
            module_name = match.group(1)
            # Get the relative path from project root
            rel_path = file_path.relative_to(Path("."))
            parts = list(rel_path.parent.parts)
            if parts and parts[0] != ".":
                absolute_path = ".".join(parts + [module_name])
                old_import = match.group(0)
                new_import = f"import {absolute_path}"
                content = content.replace(old_import, new_import)
                changes.append(f"  {old_import} -> {new_import}")

        # Write back if changes were made
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            return changes

        return []

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []


def main():
    """Main function to convert all relative imports...""""" ""
    print("🔍 Finding Python files...")
    python_files = find_python_files()
    print(f"Found {len(python_files)} Python files")

    total_changes = 0
    files_with_changes = 0

    for file_path in python_files:
        changes = convert_relative_to_absolute_import(file_path)
        if changes:
            print(f"\n📝 {file_path}:")
            for change in changes:
                print(change)
            total_changes += len(changes)
            files_with_changes += 1

    print(f"\n✅ Conversion complete!")
    print(f"  Files modified: {files_with_changes}")
    print(f"  Total changes: {total_changes}")


if __name__ == "__main__":
    main()
