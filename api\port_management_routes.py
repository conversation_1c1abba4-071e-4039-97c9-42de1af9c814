# api/port_management_routes.py
"""
Port Management API Routes
Provides REST API endpoints for dynamic port allocation and management.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from api.agent_dependency import get_agent
from core.site_container_manager import SiteContainerManager

router = APIRouter(prefix="/api/ports", tags=["Port Management"])


class PortAllocationResponse(BaseModel):
    success: bool
    message: str
    site_name: Optional[str] = None
    port: Optional[int] = None
    url: Optional[str] = None
    allocations: Optional[List[Dict[str, Any]]] = None
    available_ports: Optional[List[int]] = None
    error: Optional[str] = None


@router.post("/allocate/{site_name}", response_model=PortAllocationResponse)
async def allocate_port(site_name: str, agent=Depends(get_agent)):
    """Allocate a dynamic port for a given site"""
    try:
        manager = SiteContainerManager()
        port = manager.port_manager.allocate_port(site_name)

        return PortAllocationResponse(
            success=True,
            message=f"Allocated port {port} for site {site_name}",
            site_name=site_name,
            port=port,
            url=f"http://localhost:{port}"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/release/{site_name}", response_model=PortAllocationResponse)
async def release_port(site_name: str, agent=Depends(get_agent)):
    """Release the allocated port for a given site"""
    try:
        manager = SiteContainerManager()
        manager.port_manager.release_port(site_name)

        return PortAllocationResponse(
            success=True,
            message=f"Released port allocation for site {site_name}",
            site_name=site_name,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=PortAllocationResponse)
async def list_allocations(agent=Depends(get_agent)):
    """List all current port allocations"""
    try:
        manager = SiteContainerManager()
        allocations = manager.port_manager.list_allocations()

        formatted = [
            {"site_name": name, "port": port, "url": f"http://localhost:{port}"}
            for name, port in allocations.items()
        ]

        return PortAllocationResponse(
            success=True,
            message="Current port allocations retrieved",
            allocations=formatted,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/available", response_model=PortAllocationResponse)
async def available_ports(agent=Depends(get_agent)):
    """Get list of available ports in the configured range"""
    try:
        manager = SiteContainerManager()
        available = manager.port_manager.get_available_ports()

        return PortAllocationResponse(
            success=True,
            message="Available ports retrieved",
            available_ports=available,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

