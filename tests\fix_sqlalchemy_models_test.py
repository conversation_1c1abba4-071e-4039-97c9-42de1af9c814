import os
import sys

import pytest

# Test file for fix_sqlalchemy_models

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_fix_sqlalchemy_models_imports():
    """Test that fix_sqlalchemy_models module imports correctly."""
    try:
        import scripts.fix_sqlalchemy_models as fix_sqlalchemy_models
        assert hasattr(fix_sqlalchemy_models, 'convert_sqlalchemy_models')
        assert callable(fix_sqlalchemy_models.convert_sqlalchemy_models)
    except ImportError as e:
        pytest.fail(f"Failed to import fix_sqlalchemy_models: {e}")


def test_convert_sqlalchemy_models_function():
    """Test that convert_sqlalchemy_models function exists and is callable."""
    try:
        import scripts.fix_sqlalchemy_models as fix_sqlalchemy_models
        import tempfile
        import os

        # Create a temporary test file with SQLAlchemy 2.0 syntax
        test_content = '''from sqlalchemy.orm import Mapped, mapped_column, relationship, declarative_base
from sqlalchemy import Integer, String

class TestModel:
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(50))
'''

        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_content)
            temp_file = f.name

        try:
            # Test the conversion function
            fix_sqlalchemy_models.convert_sqlalchemy_models(temp_file)

            # Read the converted content
            with open(temp_file, 'r') as f:
                converted_content = f.read()

            # Verify conversion worked
            assert 'Mapped[' not in converted_content
            assert 'mapped_column(' not in converted_content
            assert 'Column(' in converted_content

        finally:
            # Clean up
            os.unlink(temp_file)

    except Exception as e:
        pytest.fail(f"convert_sqlalchemy_models function test failed: {e}")


def test_sqlalchemy_conversion_patterns():
    """Test that SQLAlchemy conversion patterns work correctly."""
    try:
        import scripts.fix_sqlalchemy_models as fix_sqlalchemy_models
        import re

        # Test the regex patterns used in the conversion
        test_input = 'id: Mapped[int] = mapped_column(Integer, primary_key=True)'
        expected_output = 'id = Column(Integer, primary_key=True)'

        # Apply the same regex pattern used in the function
        result = re.sub(
            r"(\w+):\s*Mapped\[[^\]]+\]\s*=\s*mapped_column\(",
            r"\1 = Column(",
            test_input
        )

        assert result == expected_output

    except Exception as e:
        pytest.fail(f"SQLAlchemy conversion patterns test failed: {e}")
