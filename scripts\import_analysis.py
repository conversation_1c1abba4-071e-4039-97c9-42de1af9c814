import ast
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

#!/usr/bin/env python3
"""
Comprehensive Import Analysis Script
Identifies import issues, circular dependencies, and missing imports in the project.
.."""TODO: Add docstring."""."""


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImportAnalyzer:
    """TODO: Add description for ImportAnalyzer..."""TODO: Add docstring."""."""TODO: Add docstring."""TODO: Add description for __init__..."""TODO: Add docstring."""."""
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.imports: Dict[str, List[str]] = {}
        self.circular_deps: List[Tuple[str, str]] = []
        self.missing_imports: List[Tuple[str, str]] = []
        self.unused_imports: List[Tuple[str, str]] = []
        self.relative_imports: List[Tuple[str, str]] = []

    def analyze_project(self) -> Dict[str, any]:
        """Analyze the entire project for import issues..."""TODO: Add docstring."""."""
        python_files = self._find_python_files()
        logger.info(f"Found {len(python_files)} Python files to analyze")

        for file_path in python_files:
            try:
                self._analyze_file(file_path)
            except Exception as e:
                logger.error(f"Error analyzing {file_path}: {e}")

        # Detect circular dependencies
        self._detect_circular_dependencies()

        return {
            'imports': self.imports,
            'circular_dependencies': self.circular_deps,
            'missing_imports': self.missing_imports,
            'unused_imports': self.unused_imports,
            'relative_imports': self.relative_imports,
            'summary': self._generate_summary()
        }

    def _find_python_files(self) -> List[Path]:
        """Find all Python files in the project..."""TODO: Add docstring."""."""
        python_files = []
        for root, dirs, files in os.walk(self.project_root):
            # Skip virtual environments and cache directories
            dirs[:] = [d for d in dirs if d not in {'.venv', '__pycache__', '.git', 'node_modules'}]

            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        return python_files

    def _analyze_file(self, file_path: Path):
        """Analyze a single Python file for import issues..."""TODO: Add docstring."""."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)
            file_imports = []

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        file_imports.append(alias.name)
                        if alias.name.startswith('.'):
                            self.relative_imports.append((str(file_path), alias.name))

                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        file_imports.append(node.module)
                        if node.level > 0:  # Relative import
                            self.relative_imports.append((str(file_path), f"{'.' * node.level}{node.module or ''}"))

            if file_imports:
                self.imports[str(file_path)] = file_imports

        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")

    def _detect_circular_dependencies(self):
        """Detect circular dependencies between modules..."""TODO: Add docstring."""."""
        # This is a simplified detection - in practice, you'd need a more sophisticated algorithm
        for file1, imports1 in self.imports.items():
            for file2, imports2 in self.imports.items():
                if file1 != file2:
                    # Check if file1 imports file2 and file2 imports file1
                    if self._files_import_each_other(file1, file2, imports1, imports2):
                        self.circular_deps.append((file1, file2))

    def _files_import_each_other(self, file1: str, file2: str, imports1: List[str], imports2: List[str]) -> bool:
        """Check if two files import each other..."""TODO: Add docstring."""."""
        # Convert file paths to module names
        module1 = self._file_to_module(file1)
        module2 = self._file_to_module(file2)

        # Check if file1 imports file2
        imports_file2 = any(module2 in imp or imp.endswith(module2) for imp in imports1)

        # Check if file2 imports file1
        imports_file1 = any(module1 in imp or imp.endswith(module1) for imp in imports2)

        return imports_file2 and imports_file1

    def _file_to_module(self, file_path: str) -> str:
        """Convert file path to module name..."""TODO: Add docstring."""."""
        rel_path = Path(file_path).relative_to(self.project_root)
        return str(rel_path).replace('/', '.').replace('\\', '.').replace('.py', '')

    def _generate_summary(self) -> Dict[str, int]:
        """Generate a summary of the analysis..."""TODO: Add docstring."""."""
        return {
            'total_files': len(self.imports),
            'circular_dependencies': len(self.circular_deps),
            'missing_imports': len(self.missing_imports),
            'unused_imports': len(self.unused_imports),
            'relative_imports': len(self.relative_imports)
        }

def main():
    """Main function to run the import analysis..."""TODO: Add docstring."""."""
    analyzer = ImportAnalyzer()
    results = analyzer.analyze_project()

    print("\n" + "="*60)
    print("IMPORT ANALYSIS RESULTS")
    print("="*60)

    # Print summary
    summary = results['summary']
    print(f"\n📊 SUMMARY:")
    print(f"  Total Python files analyzed: {summary['total_files']}")
    print(f"  Circular dependencies found: {summary['circular_dependencies']}")
    print(f"  Missing imports found: {summary['missing_imports']}")
    print(f"  Unused imports found: {summary['unused_imports']}")
    print(f"  Relative imports found: {summary['relative_imports']}")

    # Print circular dependencies
    if results['circular_dependencies']:
        print(f"\n🔄 CIRCULAR DEPENDENCIES:")
        for file1, file2 in results['circular_dependencies']:
            print(f"  {file1} ↔ {file2}")

    # Print relative imports
    if results['relative_imports']:
        print(f"\n📁 RELATIVE IMPORTS:")
        for file_path, import_stmt in results['relative_imports'][:10]:  # Show first 10
            print(f"  {file_path}: {import_stmt}")
        if len(results['relative_imports']) > 10:
            print(f"  ... and {len(results['relative_imports']) - 10} more")

    # Print recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if summary['circular_dependencies'] > 0:
        print("  ⚠️  Fix circular dependencies by restructuring imports or using lazy imports")
    if summary['relative_imports'] > 0:
        print("  ⚠️  Consider converting relative imports to absolute imports for better maintainability")
    if summary['missing_imports'] > 0:
        print("  ⚠️  Add missing imports or handle import errors gracefully")
    if summary['unused_imports'] > 0:
        print("  ⚠️  Remove unused imports to improve code quality")

    print("\n" + "="*60)

if __name__ == "__main__":
    main()
